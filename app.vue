<script setup lang="ts">
// App.vue - 应用根组件
import { onMounted } from "vue";
import { StagewiseToolbar } from "@stagewise/toolbar-vue";
import VuePlugin from "@stagewise-plugins/vue";
// 使用自动导入，无需手动导入组件

// 初始化主题
const { initTheme, theme } = useTheme();

// 在客户端初始化主题
onMounted(() => {
  initTheme();
});

// Stagewise 配置
const stagewiseConfig = {
  plugins: [VuePlugin], // Vue 插件用于 Vue 特定功能
  experimental: {
    enableStagewiseMCP: true,
    enableToolCalls: true,
  },
};

// 页面头部配置 - SSR 样式渲染已通过插件处理
// useHead 配置已简化，样式注入由 naive-ssr.server.ts 插件自动处理
</script>

<template>
  <!-- Stagewise 工具栏 - 仅在开发模式下显示 -->
  <ClientOnly>
    <StagewiseToolbar :config="stagewiseConfig" />
  </ClientOnly>

  <div id="app">
    <n-config-provider :theme="theme" :inline-theme-disabled="true">
      <n-global-style />
      <n-loading-bar-provider>
        <n-dialog-provider>
          <n-notification-provider>
            <n-message-provider>
              <NuxtLayout>
                <NuxtPage />
              </NuxtLayout>
            </n-message-provider>
          </n-notification-provider>
        </n-dialog-provider>
      </n-loading-bar-provider>
    </n-config-provider>
  </div>
</template>
