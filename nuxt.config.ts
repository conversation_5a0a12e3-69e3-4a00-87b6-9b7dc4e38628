// https://nuxt.com/docs/api/configuration/nuxt-config
import AutoImport from 'unplugin-auto-import/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'

export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  
  
  // 性能优化配置
  experimental: {
    payloadExtraction: false, // 禁用 payload 提取提升开发性能
    viewTransition: true // 启用视图过渡
  },
  
  // SSR 优化
  ssr: true,
  
  // 路由优化
  router: {
    options: {
      hashMode: false
    }
  },

  // 引入开源字体样式和主样式
  css: ['~/assets/css/fonts.css', ],

  modules: [
    '@nuxt/scripts',
    '@nuxt/image',
    '@nuxt/icon',
    '@nuxt/fonts',
    '@nuxt/eslint',
    'nuxtjs-naive-ui'
  ],
  icon: {
    provider: "iconify",
    collections: [
      'material-symbols',
      // 其他常用图标集
    ],
  },
  fonts: {
    // 配置适合中国用户的字体提供商
    providers: {
      google: false,        // 禁用 Google Fonts
      googleicons: false,   // 禁用 Google Icons
    },
    // 配置开源字体回退方案（无版权风险）
    defaults: {
      fallbacks: {
        'sans-serif': [
          // 开源中文字体（优先）
          'Source Han Sans SC',    // 思源黑体 - Adobe 开源
          'Noto Sans CJK SC',      // Google Noto 中文 - 开源
          'Source Han Sans CN',    // 思源黑体简体中文
          'Noto Sans SC',          // Noto Sans 简体中文
          // 开源英文字体
          'Inter',                 // 现代开源字体
          'Roboto',                // Google 开源字体
          'Open Sans',             // Google 开源字体
          // 系统安全字体回退
          'system-ui',             // 系统默认字体
          '-apple-system',         // Apple 系统字体
          'BlinkMacSystemFont',    // Chrome 系统字体
          'Segoe UI',              // Windows 系统字体
          'sans-serif'
        ],
        'serif': [
          // 开源中文衬线字体
          'Source Han Serif SC',   // 思源宋体 - Adobe 开源
          'Noto Serif CJK SC',     // Google Noto 衬线中文
          'Source Han Serif CN',   // 思源宋体简体中文
          // 开源英文衬线字体
          'Source Serif Pro',      // Adobe 开源衬线字体
          'Noto Serif',            // Google 开源衬线字体
          'Crimson Text',          // 开源衬线字体
          // 系统安全字体回退
          'Georgia',
          'Times New Roman',
          'serif'
        ],
        'monospace': [
          // 开源等宽字体
          'Source Code Pro',       // Adobe 开源等宽字体
          'JetBrains Mono',        // JetBrains 开源等宽字体
          'Fira Code',             // Mozilla 开源等宽字体
          'Roboto Mono',           // Google 开源等宽字体
          'Inconsolata',           // 开源等宽字体
          // 系统安全字体回退
          'Menlo',                 // macOS 等宽字体
          'Monaco',                // macOS 等宽字体
          'Consolas',              // Windows 等宽字体
          'Courier New',
          'monospace'
        ]
      }
    },
    // 开源字体本地配置（已下载到本地）
    families: [
      // 思源黑体 - Adobe 开源字体
      {
        name: 'Source Han Sans SC',
        src: '/fonts/SourceHanSansSC-Regular.otf',
        weight: '400',
        style: 'normal'
      },

      // Inter 字体 - 现代开源字体
      {
        name: 'Inter',
        src: '/fonts/Inter-Regular.woff2',
        weight: '400',
        style: 'normal'
      },

      // Inter Medium 字重
      {
        name: 'Inter',
        src: '/fonts/Inter-Medium.woff2',
        weight: '500',
        style: 'normal'
      },

      // JetBrains Mono - 开源等宽字体
      {
        name: 'JetBrains Mono',
        src: '/fonts/JetBrainsMono-Regular.woff2',
        weight: '400',
        style: 'normal'
      }
    ]
  },
  vite: {
    // 插件配置 - 自动导入
    plugins: [
      AutoImport({
        imports: [
          {
            'naive-ui': [
              'useDialog',
              'useMessage',
              'useNotification',
              'useLoadingBar'
            ]
          }
        ]
      }),
      Components({
        resolvers: [NaiveUiResolver()]
      })
    ],
    // SSR 配置 - 解决样式闪烁问题
    ssr: {
      noExternal: ['naive-ui', 'vueuc', 'date-fns', '@css-render/vue3-ssr']
    },
    // 优化开发服务器配置
    server: {
      hmr: {
        port: 24678, // 使用固定端口避免端口冲突
        overlay: false // 关闭错误覆盖层提升性能
      },
      proxy: {
        '/api': {
          target: 'http://127.0.0.1:3008',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    // CSS 优化
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "sass:math";`
        }
      },
      devSourcemap: true // 开发环境启用 CSS sourcemap
    },
    // 优化依赖预构建
    optimizeDeps: {
      include: ['naive-ui', 'vueuc', 'date-fns', '@css-render/vue3-ssr']
    }
  },

  // PostCSS 配置
  postcss: {
    plugins: {
      autoprefixer: {}
    }
  },
  // 构建配置 - 解决 vueuc 兼容性问题
  build: {
    transpile: ['naive-ui', 'vueuc', 'date-fns', '@css-render/vue3-ssr']
  }

})