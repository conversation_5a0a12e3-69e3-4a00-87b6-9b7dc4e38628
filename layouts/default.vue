<template>
  <div class="layout-container">
    <!-- Header -->
    <header class="layout-header">
      <div class="layout-content">
        <Header />
      </div>
    </header>

    <!-- Main Content -->
    <main class="layout-main">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="layout-footer">
      <div class="layout-content">
        <Footer />
      </div>
    </footer>
  </div>
</template>

<script setup>
// 使用主题
useTheme();

// 页面元数据
useHead({
  titleTemplate: "%s - 牛马学城",
  meta: [
    {
      name: "description",
      content: "牛马学城 - 专注前端开发技术分享的在线学习平台",
    },
    {
      name: "keywords",
      content:
        "前端开发,Vue.js,React,JavaScript,TypeScript,CSS,HTML,在线学习,编程教程",
    },
    { name: "author", content: "牛马学城" },
    { property: "og:site_name", content: "牛马学城" },
    { property: "og:type", content: "website" },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:site", content: "@niumacity" },
  ],
  link: [{ rel: "icon", type: "image/x-icon", href: "/favicon.ico" }],
});
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--body-color, #ffffff);
}

.layout-header {
  position: sticky;
  top: 0;
  z-index: 50;
  background-color: var(--header-bg, rgba(255, 255, 255, 0.8));
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.layout-content {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .layout-content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .layout-content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.layout-main {
  flex-grow: 1;
}

.layout-footer {
  background-color: var(--bg-secondary, #f9fafb);
  border-top: 1px solid var(--border-color, #e5e7eb);
}
</style>
