{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "dev:fast": "nuxt dev --config nuxt.config.dev.ts", "dev:optimized": "nuxt dev --config nuxt.config.dev.ts --no-clear", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "analyze": "nuxt build --analyze", "clean": "rm -rf .nuxt dist .output"}, "dependencies": {"@iconify/json": "^2.2.349", "@nuxt/eslint": "1.4.1", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.13.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.8", "@stagewise-plugins/vue": "^0.6.2", "@unhead/vue": "^2.0.3", "better-sqlite3": "^11.10.0", "eslint": "^9.0.0", "naive-ui": "^2.42.0", "nuxt": "^3.17.5", "nuxtjs-naive-ui": "^1.0.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@css-render/vue3-ssr": "^0.15.14", "@stagewise/toolbar-vue": "^0.6.2", "autoprefixer": "^10.4.21"}}