<template>
  <div class="home-container">
    <!-- Hero Section -->
    <div class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            专注AI工具提效，<span class="hero-highlight">让AI当牛马</span>
          </h1>
          <p class="hero-subtitle">
            牛马学城提供高质量的编程课程和技术文章，帮助您在快速发展的技术世界中保持领先。
          </p>
          <div class="hero-buttons">
            <n-button
              type="primary"
              size="large"
              strong
              class="hero-button-primary"
              @click="$router.push('/membership')"
            >
              开始学习
            </n-button>
            <n-button
              size="large"
              strong
              secondary
              class="hero-button-secondary"
              @click="$router.push('/articles')"
            >
              浏览文章
            </n-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="features-section">
      <div class="features-content">
        <div class="features-header">
          <h2 class="features-title">为什么选择牛马学城？</h2>
          <p class="features-subtitle">
            我们提供高质量的学习资源，帮助您快速掌握最新技术。
          </p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon-container feature-icon-blue">
              <svg
                class="feature-icon"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
            </div>
            <h3 class="feature-title">高质量课程</h3>
            <p class="feature-description">
              精心设计的课程内容，由行业专家制作，确保您学到最实用的技术知识。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon-container feature-icon-green">
              <svg
                class="feature-icon"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
            </div>
            <h3 class="feature-title">实战项目</h3>
            <p class="feature-description">
              通过实际项目练习，巩固所学知识，提升解决实际问题的能力。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon-container feature-icon-purple">
              <svg
                class="feature-icon"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
            </div>
            <h3 class="feature-title">前沿资讯</h3>
            <p class="feature-description">
              获取最新的技术资讯，紧跟行业发展步伐。
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="cta-section">
      <div class="cta-content">
        <div class="cta-card">
          <h2 class="cta-title">开始您的学习之旅</h2>
          <p class="cta-subtitle">
            立即加入牛马学城，获取海量优质课程资源，提升您的技术能力。
          </p>
          <n-button
            type="primary"
            size="large"
            strong
            secondary
            class="cta-button"
            @click="$router.push('/membership')"
          >
            立即加入
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 定义页面元数据
useHead({
  title: "牛马学城 - 专业的在线教育平台",
  meta: [
    {
      name: "description",
      content:
        "牛马学城是一个专业的在线教育平台，提供高质量的编程课程和技术文章，助力你的技术成长之路。",
    },
    {
      name: "keywords",
      content: "在线教育,编程课程,技术文章,前端开发,后端开发,全栈开发",
    },
  ],
});
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background-color: var(--body-color, #ffffff);
}

/* Hero Section */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 6rem;
  padding-bottom: 6rem;
}

@media (min-width: 640px) {
  .hero-content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .hero-content {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}

@media (min-width: 1024px) {
  .hero-content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.hero-text {
  text-align: center;
}

.hero-title {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-primary, #111827);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 3.75rem;
  }
}

.hero-highlight {
  color: #2563eb;
}

.hero-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary, #4b5563);
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 2.5rem;
  line-height: 1.75rem;
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
}

@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
    gap: 1rem;
  }
}

.hero-button-primary,
.hero-button-secondary {
  padding-left: 2rem;
  padding-right: 2rem;
  justify-content: center;
}

/* Features Section */
.features-section {
  padding-top: 4rem;
  padding-bottom: 4rem;
  background-color: var(--bg-secondary, #f9fafb);
}

.features-content {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .features-content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .features-content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.features-header {
  text-align: center;
  margin-bottom: 4rem;
}

.features-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary, #111827);
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .features-title {
    font-size: 2.25rem;
  }
}

.features-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary, #4b5563);
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.75rem;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

.feature-card {
  background-color: var(--bg-primary, #ffffff);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.feature-card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.feature-icon-container {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.feature-icon-blue {
  background-color: #dbeafe;
}

.feature-icon-green {
  background-color: #dcfce7;
}

.feature-icon-purple {
  background-color: #f3e8ff;
}

.feature-icon {
  width: 1.5rem;
  height: 1.5rem;
}

.feature-icon-blue .feature-icon {
  color: #2563eb;
}

.feature-icon-green .feature-icon {
  color: #16a34a;
}

.feature-icon-purple .feature-icon {
  color: #9333ea;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #111827);
  margin-bottom: 0.75rem;
}

.feature-description {
  color: var(--text-secondary, #4b5563);
  line-height: 1.625;
}

/* CTA Section */
.cta-section {
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.cta-content {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .cta-content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .cta-content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.cta-card {
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 1.5rem;
  padding: 3rem;
  text-align: center;
}

.cta-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .cta-title {
    font-size: 2.25rem;
  }
}

.cta-subtitle {
  font-size: 1.125rem;
  color: #dbeafe;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 2.5rem;
  line-height: 1.75rem;
}

.cta-button {
  background-color: #ffffff;
  color: #2563eb;
  padding-left: 2rem;
  padding-right: 2rem;
  justify-content: center;
}

.cta-button:hover {
  background-color: #eff6ff;
}
</style>
