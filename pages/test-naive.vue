<template>
  <div class="test-page">
    <h1>NaiveUI 测试页面</h1>

    <n-space vertical>
      <n-button type="primary">主要按钮</n-button>
      <n-button type="success">成功按钮</n-button>
      <n-button type="warning">警告按钮</n-button>
      <n-button type="error">错误按钮</n-button>

      <n-input placeholder="请输入内容" />

      <n-card title="测试卡片">
        这是一个测试卡片，用于验证NaiveUI样式是否正常加载。
      </n-card>

      <n-alert type="info">
        如果您能看到这些组件的正确样式，说明NaiveUI配置成功！
      </n-alert>
    </n-space>
  </div>
</template>

<script setup>
// 使用自动导入，无需手动导入组件

// 页面元数据
useHead({
  title: "NaiveUI 测试页面",
});
</script>

<style scoped>
.test-page {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  margin-bottom: 2rem;
  color: var(--text-primary);
}
</style>
