<template>
  <div class="articles-container">
    <!-- 左侧导航栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h2 class="sidebar-title">精选</h2>
      </div>
      <nav class="nav-links">
        <NuxtLink to="/articles" class="nav-item active">
          <Icon name="material-symbols:recommend" class="nav-icon" />
          推荐
        </NuxtLink>
        <NuxtLink to="/articles/latest" class="nav-item">
          <Icon name="material-symbols:new-releases" class="nav-icon" />
          最新
        </NuxtLink>
        <NuxtLink to="/articles/tony-bai" class="nav-item">
          <div class="author-avatar">TB</div>
          <PERSON> 说
        </NuxtLink>
        <NuxtLink to="/articles/fengjian" class="nav-item">
          <div class="author-avatar">风</div>
          风间影月说
        </NuxtLink>
        <NuxtLink to="/articles/frontend" class="nav-item">
          <Icon name="material-symbols:code" class="nav-icon" />
          前端开发
        </NuxtLink>
        <NuxtLink to="/articles/backend" class="nav-item">
          <Icon name="material-symbols:dns" class="nav-icon" />
          后端开发
        </NuxtLink>
        <NuxtLink to="/articles/dialogue" class="nav-item">
          <Icon name="material-symbols:chat" class="nav-icon" />
          对话 ChatGPT
        </NuxtLink>
        <NuxtLink to="/articles/mobile" class="nav-item">
          <Icon name="material-symbols:smartphone" class="nav-icon" />
          移动开发
        </NuxtLink>
        <NuxtLink to="/articles/cloud" class="nav-item">
          <Icon name="material-symbols:cloud" class="nav-icon" />
          云计算/大数据
        </NuxtLink>
        <NuxtLink to="/articles/product" class="nav-item">
          <Icon name="material-symbols:design-services" class="nav-icon" />
          产品设计
        </NuxtLink>
      </nav>
    </aside>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 顶部搜索栏 -->
      <div class="top-search">
        <div class="search-container">
          <n-input
            v-model:value="searchQuery"
            placeholder="搜索感兴趣的知识和文章"
            size="large"
            clearable
            class="search-input"
          >
            <template #prefix>
              <Icon name="material-symbols:search" class="search-icon" />
            </template>
          </n-input>
          <n-button type="primary" size="large" round class="write-btn">
            写文章
          </n-button>
        </div>
      </div>

      <!-- 热门作者卡片 -->
      <div class="featured-authors">
        <div class="author-card tony-bai">
          <div class="author-info">
            <div class="author-avatar-large">
              <img
                src="https://placehold.co/60x60/007AFF/white?text=TB"
                alt="Tony Bai"
              />
            </div>
            <div class="author-details">
              <h3 class="author-name">Tony Bai 说</h3>
              <p class="author-desc">
                创业公司技术负责人，10年+开发和技术管理经验。SUNLI认证SCJP、PMP、MCP认证。主要做电商平台与微信小程序的架构设计和开发实践。
              </p>
              <div class="author-stats">
                <span class="stat-item">
                  <Icon name="material-symbols:group" />
                  1.2万用户正在围观
                </span>
              </div>
            </div>
          </div>
          <n-button text type="primary" class="enter-btn">
            进入讨论 →
          </n-button>
        </div>

        <div class="author-card fengjian">
          <div class="author-info">
            <div class="author-avatar-large">
              <img
                src="https://placehold.co/60x60/34C759/white?text=风"
                alt="风间影月"
              />
            </div>
            <div class="author-details">
              <h3 class="author-name">风间影月说</h3>
              <p class="author-desc">
                Tony
                Bai，智能网联汽车领域专家公司先行研发部负责人，Go语言专家，资深架构师之《Go语言精进之路》作者。
              </p>
              <div class="author-stats">
                <span class="stat-item">
                  <Icon name="material-symbols:group" />
                  8.5k用户正在围观
                </span>
              </div>
            </div>
          </div>
          <n-button text type="primary" class="enter-btn">
            进入讨论 →
          </n-button>
        </div>
      </div>

      <!-- 文章列表 -->
      <div class="articles-list">
        <div
          v-for="article in filteredArticles"
          :key="article.id"
          class="article-item"
        >
          <div class="article-icon">
            <Icon :name="article.icon" class="topic-icon" />
          </div>
          <div class="article-content">
            <h3 class="article-title">{{ article.title }}</h3>
            <div class="article-meta">
              <span class="meta-item">{{ article.author }}</span>
              <span class="meta-separator">•</span>
              <span class="meta-item">{{ article.views }} 浏览</span>
              <span class="meta-separator">•</span>
              <span class="meta-item">{{ article.category }}</span>
              <span class="meta-separator">•</span>
              <span class="meta-item">{{ article.tags.join("、") }}</span>
            </div>
          </div>
          <div class="article-time">{{ article.time }}</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredArticles.length === 0" class="empty-state">
        <Icon name="material-symbols:search-off" class="empty-icon" />
        <h3 class="empty-title">没有找到相关文章</h3>
        <p class="empty-description">
          抱歉，我们没有找到与 "{{ searchQuery }}" 相关的文章。
        </p>
        <n-button type="primary" round @click="resetFilters">
          查看所有文章
        </n-button>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
// 使用自动导入，无需手动导入组件

// 页面元数据
useHead({
  title: "技术文章 - 牛马学城",
  meta: [
    {
      name: "description",
      content: "探索最新的技术文章和教程，提升你的编程技能",
    },
  ],
});

// 搜索状态
const searchQuery = ref("");

// 模拟文章数据
const articles = ref([
  {
    id: 1,
    title:
      "第一批吃到「物联网嵌入式开发」红利的人已经领先，全新升级更多福利来袭！",
    author: "后端开发",
    views: "17883",
    category: "嵌入式开发",
    tags: ["C++", "嵌入式"],
    time: "11:03",
    icon: "material-symbols:developer-board",
  },
  {
    id: 2,
    title: "营销手记社区指南",
    author: "营销",
    views: "100088",
    category: "营销策略",
    tags: ["运营中心", "营销"],
    time: "08:09",
    icon: "material-symbols:campaign",
  },
  {
    id: 3,
    title: "我时1年半的DBA体系架构已经架构组",
    author: "云计算/大数据",
    views: "44336",
    category: "数据库",
    tags: ["MongoDB", "MySQL", "Redis"],
    time: "01:29",
    icon: "material-symbols:database",
  },
  {
    id: 4,
    title: "Vue 3 Composition API 完全指南",
    author: "前端开发",
    views: "12456",
    category: "前端开发",
    tags: ["Vue.js", "JavaScript"],
    time: "昨天",
    icon: "material-symbols:code",
  },
  {
    id: 5,
    title: "React Hooks 使用最佳实践",
    author: "前端开发",
    views: "8932",
    category: "前端开发",
    tags: ["React", "Hooks"],
    time: "2天前",
    icon: "material-symbols:code",
  },
  {
    id: 6,
    title: "Node.js 性能优化技巧",
    author: "后端开发",
    views: "15678",
    category: "后端开发",
    tags: ["Node.js", "性能优化"],
    time: "3天前",
    icon: "material-symbols:dns",
  },
  {
    id: 7,
    title: "TypeScript 高级类型详解",
    author: "前端开发",
    views: "9876",
    category: "前端开发",
    tags: ["TypeScript", "类型系统"],
    time: "4天前",
    icon: "material-symbols:code",
  },
  {
    id: 8,
    title: "微服务架构设计模式",
    author: "后端开发",
    views: "11234",
    category: "架构设计",
    tags: ["微服务", "架构"],
    time: "5天前",
    icon: "material-symbols:architecture",
  },
]);

// 过滤后的文章
const filteredArticles = computed(() => {
  if (!searchQuery.value) return articles.value;

  return articles.value.filter((article) => {
    const searchTerm = searchQuery.value.toLowerCase();
    return (
      article.title.toLowerCase().includes(searchTerm) ||
      article.category.toLowerCase().includes(searchTerm) ||
      article.tags.some((tag) => tag.toLowerCase().includes(searchTerm))
    );
  });
});

// 重置筛选器
const resetFilters = () => {
  searchQuery.value = "";
};
</script>

<style scoped>
/* 整体容器 */
.articles-container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  display: flex;
}

/* 左侧导航栏 */
.sidebar {
  width: 240px;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  padding: 24px 0;
  overflow-y: auto;
  margin: 20px 20px 20px 20px;
  align-self: flex-start;
  border-radius: 8px;
}

.sidebar-header {
  padding: 0 24px 16px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.nav-links {
  padding: 0;
  margin: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.2s ease;
  border-radius: 0;
}

.nav-item:hover {
  background-color: var(--hover-bg-color);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: #007aff;
  color: #ffffff;
}

.nav-icon {
  width: 16px;
  height: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.author-avatar {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007aff, #5856d6);
  color: white;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  margin: 20px 60px 20px 0;
  padding: 0;
  background-color: var(--bg-primary);
  border-radius: 8px;
}

/* 顶部搜索栏 */
.top-search {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 20px 32px;
  position: sticky;
  top: 0;
  z-index: 5;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.search-input {
  flex: 1;
  max-width: 600px;
}

.search-input :deep(.n-input) {
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.search-icon {
  color: #9ca3af;
}

.write-btn {
  background: linear-gradient(135deg, #007aff, #5856d6);
  border: none;
  padding: 0 24px;
  height: 40px;
  border-radius: 8px;
}

/* 热门作者卡片 */
.featured-authors {
  padding: 32px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.author-card {
  background-color: var(--bg-primary);
  border-radius: 8px;
  padding: 24px;
  border: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.2s ease;
}

.author-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.author-info {
  display: flex;
  gap: 16px;
  flex: 1;
}

.author-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.author-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-details {
  flex: 1;
}

.author-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.author-desc {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.author-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8e8e93;
}

.enter-btn {
  color: #007aff;
  font-weight: 500;
  padding: 8px 0;
}

/* 文章列表 */
.articles-list {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.article-item {
  background-color: var(--bg-primary);
  border-radius: 8px;
  padding: 20px 24px;
  margin-bottom: 12px;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.article-item:hover {
  background-color: var(--hover-bg-color);
  border-color: var(--border-color);
}

.article-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #007aff, #5856d6);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.topic-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.article-content {
  flex: 1;
  min-width: 0;
}

.article-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #8e8e93;
}

.meta-item {
  white-space: nowrap;
}

.meta-separator {
  color: #d1d5db;
}

.article-time {
  font-size: 13px;
  color: #8e8e93;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 32px;
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  width: 64px;
  height: 64px;
  color: #d1d5db;
  margin: 0 auto 24px;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 14px;
  color: #8e8e93;
  line-height: 1.5;
  margin: 0 0 32px 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .featured-authors {
    grid-template-columns: 1fr;
    padding: 24px;
  }

  .articles-list {
    padding: 0 24px 24px;
  }

  .search-container {
    padding: 0 24px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    margin-left: 0;
  }

  .main-content {
    margin: 20px;
  }

  .search-container {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .featured-authors {
    padding: 16px;
  }

  .author-card {
    flex-direction: column;
    gap: 16px;
  }

  .article-item {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .article-meta {
    flex-wrap: wrap;
  }
}
</style>
