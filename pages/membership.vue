<template>
  <div class="membership-container">
    <div class="membership-content">
      <!-- 页面头部 -->
      <div class="membership-header">
        <h1 class="membership-title">
          选择适合你的会员计划
        </h1>
        <p class="membership-subtitle">
          解锁全部课程内容，获得专属学习资源，加速你的技术成长之路
        </p>
      </div>

      <!-- 会员计划 -->
      <div class="membership-plans">
        <!-- 基础会员 -->
        <div class="membership-card">
          <div class="card-header">
            <h3 class="plan-name">基础会员</h3>
            <p class="plan-description">适合初学者</p>
            <div class="plan-price">
              <span class="price-amount">¥99</span>
              <span class="price-period">/月</span>
            </div>
          </div>
          
          <ul class="plan-features">
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">访问基础课程</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">部分文章阅读</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">基础练习项目</span>
            </li>
            <li class="feature-item feature-item-disabled">
              <svg class="feature-icon feature-icon-cross" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span class="feature-text feature-text-disabled">高级课程访问</span>
            </li>
            <li class="feature-item feature-item-disabled">
              <svg class="feature-icon feature-icon-cross" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span class="feature-text feature-text-disabled">专属学习社群</span>
            </li>
          </ul>
          
          <n-button 
            block 
            size="large" 
            strong 
            secondary
            class="plan-button"
          >
            开始学习
          </n-button>
        </div>
        
        <!-- 标准会员 -->
        <div class="membership-card membership-card-popular">
          <div class="popular-badge">最受欢迎</div>
          <div class="card-header">
            <h3 class="plan-name">标准会员</h3>
            <p class="plan-description">适合进阶开发者</p>
            <div class="plan-price">
              <span class="price-amount">¥199</span>
              <span class="price-period">/月</span>
            </div>
          </div>
          
          <ul class="plan-features">
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">访问所有课程</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">全部文章阅读</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">完整练习项目</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">高级课程访问</span>
            </li>
            <li class="feature-item feature-item-disabled">
              <svg class="feature-icon feature-icon-cross" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span class="feature-text feature-text-disabled">专属学习社群</span>
            </li>
          </ul>
          
          <n-button 
            block 
            size="large" 
            type="primary" 
            strong
            class="plan-button"
          >
            立即购买
          </n-button>
        </div>
        
        <!-- 高级会员 -->
        <div class="membership-card">
          <div class="card-header">
            <h3 class="plan-name">高级会员</h3>
            <p class="plan-description">适合专业开发者</p>
            <div class="plan-price">
              <span class="price-amount">¥299</span>
              <span class="price-period">/月</span>
            </div>
          </div>
          
          <ul class="plan-features">
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">访问所有课程</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">全部文章阅读</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">完整练习项目</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">高级课程访问</span>
            </li>
            <li class="feature-item">
              <svg class="feature-icon feature-icon-check" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span class="feature-text">专属学习社群</span>
            </li>
          </ul>
          
          <n-button 
            block 
            size="large" 
            strong 
            secondary
            class="plan-button"
          >
            立即购买
          </n-button>
        </div>
      </div>

      <!-- FAQ -->
      <div class="faq-section">
        <h2 class="faq-title">常见问题</h2>
        <div class="faq-list">
          <div class="faq-item">
            <h3 class="faq-question">会员权益有哪些区别？</h3>
            <p class="faq-answer">
              基础会员可以访问部分基础课程和文章；标准会员可以访问所有课程和文章；高级会员除了拥有标准会员的所有权益外，还可以加入专属学习社群，与更多开发者交流学习。
            </p>
          </div>
          
          <div class="faq-item">
            <h3 class="faq-question">如何取消订阅？</h3>
            <p class="faq-answer">
              您可以随时在账户设置中取消订阅。取消后，您仍可以使用会员权益直到当前订阅周期结束。
            </p>
          </div>
          
          <div class="faq-item">
            <h3 class="faq-question">支持退款吗？</h3>
            <p class="faq-answer">
              我们提供7天无理由退款服务。如果您在购买后7天内发现我们的服务不符合您的需求，可以联系客服申请退款。
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 页面元数据
useHead({
  title: '会员计划 - 牛马学城',
  meta: [
    { name: 'description', content: '选择适合您的会员计划，解锁全部课程内容，获得专属学习资源。' },
    { name: 'keywords', content: '会员计划,在线学习,编程课程,技术文章' }
  ]
})
</script>

<style scoped>
.membership-container {
  min-height: 100vh;
  background-color: #ffffff;
  padding-top: 4rem;
  padding-bottom: 4rem;
}

:global(html.dark) .membership-container {
  background-color: #111827;
}

.membership-content {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .membership-content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .membership-content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* 页面头部 */
.membership-header {
  text-align: center;
  margin-bottom: 4rem;
}

.membership-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .membership-title {
    font-size: 2.25rem;
  }
}

:global(html.dark) .membership-title {
  color: #ffffff;
}

.membership-subtitle {
  font-size: 1.125rem;
  color: #4b5563;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.75rem;
}

:global(html.dark) .membership-subtitle {
  color: #d1d5db;
}

/* 会员计划 */
.membership-plans {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .membership-plans {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

.membership-card {
  background-color: #ffffff;
  border-radius: 1.5rem;
  border: 1px solid #e5e7eb;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  position: relative;
}

.membership-card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

:global(html.dark) .membership-card {
  background-color: #1f2937;
  border: 1px solid #374151;
}

.membership-card-popular {
  border: 1px solid #3b82f6;
}

.popular-badge {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  background-color: #3b82f6;
  color: #ffffff;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 1rem;
  border-radius: 9999px;
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

:global(html.dark) .plan-name {
  color: #ffffff;
}

.plan-description {
  color: #4b5563;
  margin-bottom: 1.5rem;
}

:global(html.dark) .plan-description {
  color: #9ca3af;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.price-amount {
  font-size: 2.25rem;
  font-weight: 700;
  color: #111827;
}

:global(html.dark) .price-amount {
  color: #ffffff;
}

.price-period {
  margin-left: 0.25rem;
  color: #4b5563;
}

:global(html.dark) .price-period {
  color: #9ca3af;
}

.plan-features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.feature-item-disabled {
  opacity: 0.5;
}

.feature-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
}

.feature-icon-check {
  color: #16a34a;
}

.feature-icon-cross {
  color: #6b7280;
}

.feature-text {
  color: #4b5563;
}

:global(html.dark) .feature-text {
  color: #9ca3af;
}

.feature-text-disabled {
  text-decoration: line-through;
}

.plan-button {
  justify-content: center;
}

/* FAQ */
.faq-section {
  margin-top: 6rem;
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
}

.faq-title {
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  color: #111827;
  margin-bottom: 3rem;
}

:global(html.dark) .faq-title {
  color: #ffffff;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.faq-item {
  background-color: #f9fafb;
  border-radius: 1.5rem;
  padding: 1.5rem;
}

:global(html.dark) .faq-item {
  background-color: rgba(31, 41, 55, 0.5);
}

.faq-question {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

:global(html.dark) .faq-question {
  color: #ffffff;
}

.faq-answer {
  color: #4b5563;
  line-height: 1.625;
}

:global(html.dark) .faq-answer {
  color: #9ca3af;
}
</style>