import { setup } from '@css-render/vue3-ssr'
import { defineNuxtPlugin } from '#imports'

/**
 * NaiveUI SSR 样式渲染插件
 * 解决服务端渲染时样式闪烁问题
 * 
 * 工作原理：
 * 1. 在服务端收集 NaiveUI 组件的样式
 * 2. 将样式注入到 HTML 头部
 * 3. 确保客户端水合时样式已经存在
 */
export default defineNuxtPlugin((nuxtApp) => {
  // 只在服务端执行
  if (process.server) {
    // 设置 CSS 渲染收集器
    const { collect } = setup(nuxtApp.vueApp)
    
    // 在 SSR 上下文中注入样式
    nuxtApp.ssrContext!.head.push({
      style: () => collect()
        .split('<style cssr-id')
        .map((block) => {
          // 提取样式 ID
          const id = /cssr-id="(.+?)"/.exec(block)?.[1]
          // 提取样式内容
          const style = (/>(.*)$/s.exec(block)?.[1] ?? '').trim()
          
          // 返回格式化的样式对象
          return {
            'cssr-id': id,
            'innerHTML': style,
          }
        })
        .filter(item => item.innerHTML) // 过滤空样式
    })
  }
})
