# 牛码城 API 文档

## 概述

本文档描述了牛码城平台的所有 API 接口，包括前端用户接口、管理后台接口、分销商接口和讲师接口。

## 基础信息

- **API 版本**: v1
- **基础路径**: 
  - 前端用户: `/v1`
  - 管理后台: `/v1/backend`
  - 分销商端: `/v1/distributor`
  - 讲师端: `/v1/instructor`
- **认证方式**: Bearer Token (JWT)
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 认证说明

### Token 类型
- **用户 Token**: 前端用户认证，通过 `AuthJWT()` 中间件验证
- **管理员 Token**: 后台管理员认证，通过 `AdminJWT()` 中间件验证
- **分销商 Token**: 分销商认证，通过 `DistributorJWT()` 中间件验证
- **讲师 Token**: 讲师认证，通过 `InstructorJWT()` 中间件验证
- **访客 Token**: 部分接口支持访客访问，通过 `GuestJWT()` 中间件验证

### 请求头格式
```
Authorization: Bearer <token>
Content-Type: application/json
```

## 限流说明

- **前端用户**: 每小时 1000 次请求
- **管理后台**: 每小时 5000 次请求
- **分销商端**: 每小时 2000 次请求
- **讲师端**: 每小时 3000 次请求
- **认证接口**: 每小时 200 次请求
- **验证码接口**: 每小时 10-30 次请求

---

## 一、前端用户接口 (`/v1`)

### 1.1 通用视频接口

#### 获取视频播放签名
**GET** `/v1/videos/play-signature`

**权限**: 需要用户认证

**查询参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| video_id | string | 是 | 视频ID |

**响应示例**:
```json
{
  "signature": "video_play_signature",
  "expires_at": "2025-07-30T12:00:00Z"
}
```

### 1.2 用户认证模块 (`/v1/auth`)

#### 检查手机号是否已注册
**POST** `/v1/auth/signup/phone/exist`

**权限**: 访客可访问

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应示例**:
```json
{
  "exist": true
}
```

#### 检查用户名是否已注册
**POST** `/v1/auth/signup/name/exist`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "name": "username"
}
```

#### 检查邮箱是否已注册
**POST** `/v1/auth/signup/email/exist`

**权限**: 访客可访问

**请求参数**:
```json
{
  "email": "<EMAIL>"
}
```

#### 手机号注册
**POST** `/v1/auth/signup/using-phone`

**权限**: 访客可访问

**请求参数**:
```json
{
  "phone": "13800138000",
  "name": "用户名",
  "password": "password123",
  "password_confirm": "password123",
  "verify_code": "123456"
}
```

#### 邮箱注册
**POST** `/v1/auth/signup/using-email`

**权限**: 访客可访问

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "name": "用户名",
  "password": "password123",
  "password_confirm": "password123",
  "verify_code": "123456"
}
```

#### 获取图片验证码
**POST** `/v1/auth/verify-codes/captcha`

**限流**: 每小时 30 次

**响应示例**:
```json
{
  "captcha_id": "captcha_id_string",
  "captcha_image": "base64_encoded_image"
}
```

#### 发送手机验证码
**POST** `/v1/auth/verify-codes/phone`

**限流**: 每小时 10 次

**请求参数**:
```json
{
  "phone": "13800138000",
  "captcha_id": "captcha_id_string",
  "captcha_answer": "验证码答案"
}
```

#### 发送邮箱验证码
**POST** `/v1/auth/verify-codes/email`

**限流**: 每小时 15 次

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "captcha_id": "captcha_id_string",
  "captcha_answer": "验证码答案"
}
```

#### 手机号登录
**POST** `/v1/auth/login/using-phone`

**权限**: 访客可访问

**请求参数**:
```json
{
  "phone": "13800138000",
  "verify_code": "123456"
}
```

**响应示例**:
```json
{
  "token": "jwt_token_string",
  "user": {
    "id": 1,
    "name": "用户名",
    "phone": "13800138000",
    "email": "<EMAIL>"
  }
}
```

#### 密码登录
**POST** `/v1/auth/login/using-password`

**权限**: 访客可访问

**请求参数**:
```json
{
  "login": "13800138000",
  "password": "password123"
}
```

#### 刷新Token
**POST** `/v1/auth/login/refresh-token`

**请求参数**:
```json
{
  "refresh_token": "refresh_token_string"
}
```

#### 手机号重置密码
**POST** `/v1/auth/password-reset/using-phone`

**权限**: 访客可访问

**请求参数**:
```json
{
  "phone": "13800138000",
  "verify_code": "123456",
  "password": "new_password123",
  "password_confirm": "new_password123"
}
```

#### 邮箱重置密码
**POST** `/v1/auth/password-reset/using-email`

**权限**: 访客可访问

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "verify_code": "123456",
  "password": "new_password123",
  "password_confirm": "new_password123"
}
```

#### 获取用户总数统计
**GET** `/v1/auth/stat/count`

**响应示例**:
```json
{
  "total_users": 10000
}
```

### 1.3 用户信息管理

#### 获取当前用户信息
**GET** `/v1/user`

**权限**: 需要用户认证

**响应示例**:
```json
{
  "id": 1,
  "name": "用户名",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "avatar": "avatar_url",
  "created_at": "2025-07-30T10:00:00Z"
}
```

### 1.4 用户资料管理 (`/v1/users`)

#### 更新用户资料
**PUT** `/v1/users`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "name": "新用户名",
  "introduction": "个人简介"
}
```

#### 更新邮箱
**PUT** `/v1/users/email`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "verify_code": "123456"
}
```

#### 更新手机号
**PUT** `/v1/users/phone`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "phone": "13800138001",
  "verify_code": "123456"
}
```

#### 更新密码
**PUT** `/v1/users/password`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "old_password": "old_password123",
  "password": "new_password123",
  "password_confirm": "new_password123"
}
```

#### 更新头像
**PUT** `/v1/users/avatar`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "avatar": "new_avatar_url"
}
```

#### 上传头像
**POST** `/v1/users/upload-avatar`

**权限**: 需要用户认证

**请求类型**: `multipart/form-data`

**请求参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| avatar | file | 是 | 头像文件 |

#### 更新微信信息
**PUT** `/v1/users/wechat`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "wechat": "微信号"
}
```

#### 获取微信信息
**GET** `/v1/users/wechat`

**权限**: 需要用户认证

#### 获取用户信息
**GET** `/v1/users/userinfo`

**权限**: 需要用户认证

#### 获取用户项目
**GET** `/v1/users/project/{user_id}`

**权限**: 需要用户认证

#### 更新信息显示设置
**PUT** `/v1/users/show`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "show_phone": true,
  "show_email": false
}
```

### 1.5 签署记录管理

#### 获取签署记录列表
**GET** `/v1/users/signature-record`

**权限**: 需要用户认证

#### 搜索签署记录
**GET** `/v1/users/signature-record/search`

**权限**: 需要用户认证

**查询参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| keyword | string | 否 | 搜索关键词 |

#### 创建签署记录
**POST** `/v1/users/signature-record`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "agreement_id": 1,
  "signature": "用户签名"
}
```

#### 获取签署状态
**GET** `/v1/users/signature-record/status`

**权限**: 需要用户认证

#### 获取用户协议列表
**GET** `/v1/users/user-agreement`

**权限**: 需要用户认证

### 1.6 友情链接

#### 获取友情链接列表
**GET** `/v1/links`

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "name": "链接名称",
      "url": "https://example.com",
      "sort": 1
    }
  ]
}
```

### 1.7 学习路线

#### 获取学习路线列表
**GET** `/v1/learn-route`

#### 获取学习路线课程
**GET** `/v1/learn-route/courses/{id}`

### 1.8 课程模块 (`/v1/courses`)

#### 获取课程列表
**GET** `/v1/courses`

**查询参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| keyword | string | 否 | 搜索关键词 |
| category_id | int | 否 | 分类ID |
| page | int | 否 | 页码，默认1 |
| per_page | int | 否 | 每页数量，默认15 |

#### 获取课程详情
**GET** `/v1/courses/{id}`

### 1.9 学习记录模块 (`/v1/learning`)

**权限**: 所有接口都需要用户认证

#### 更新课程学习进度
**PUT** `/v1/learning/courses/{courseId}/progress`

**请求参数**:
```json
{
  "progress": 75,
  "current_chapter": 5
}
```

#### 获取课程学习进度
**GET** `/v1/learning/courses/{courseId}/progress`

#### 获取用户学习统计
**GET** `/v1/learning/stats`

#### 开始文档阅读会话
**POST** `/v1/learning/documents/{documentId}/session/start`

#### 更新文档阅读会话
**PUT** `/v1/learning/documents/{documentId}/session`

#### 结束文档阅读会话
**POST** `/v1/learning/documents/{documentId}/session/end`

#### 获取阅读统计
**GET** `/v1/learning/reading/stats`

### 1.10 系统课程模块 (`/v1/system-courses`)

#### 获取系统课程文档列表
**GET** `/v1/system-courses/books`

#### 获取系统课程列表
**GET** `/v1/system-courses`

#### 获取系统课程用户信息
**GET** `/v1/system-courses/system-class-user`

**权限**: 需要用户认证

#### 获取班级详情
**GET** `/v1/system-courses/classes/{id}`

**权限**: 需要用户认证

### 1.11 文档模块 (`/v1/books`)

#### 获取文档分类列表
**GET** `/v1/books/categories`

#### 根据分类获取文档列表
**GET** `/v1/books/{cate_id}`

#### 获取文档详情
**GET** `/v1/books/detail/{id}`

**权限**: 需要用户认证

#### 获取文章详情
**GET** `/v1/books/article/detail/{id}`

### 1.12 文档内容模块 (`/v1/books/documents`)

#### 获取文档内容详情
**GET** `/v1/books/documents/detail/{id}`

**权限**: 需要用户认证

#### 获取文章内容详情
**GET** `/v1/books/documents/article/detail/{id}`

#### 获取文档评论列表
**GET** `/v1/books/documents/{book_document_id}/comments`

#### 获取单个评论详情
**GET** `/v1/books/documents/comments/{id}`

#### 创建文档评论
**POST** `/v1/books/documents/{book_document_id}/comments`

**权限**: 需要用户认证

**请求参数**:
```json
{
  "content": "评论内容",
  "parent_id": 0
}
```

#### 更新评论
**PUT** `/v1/books/documents/comments/{id}`

**权限**: 需要用户认证

#### 删除评论
**DELETE** `/v1/books/documents/comments/{id}`

**权限**: 需要用户认证

#### 上传评论图片
**POST** `/v1/books/documents/comments/upload-image`

**权限**: 需要用户认证

**请求类型**: `multipart/form-data`

#### 批量上传评论图片
**POST** `/v1/books/documents/comments/upload-images`

**权限**: 需要用户认证

**请求类型**: `multipart/form-data`

#### 获取文档用户信息
**GET** `/v1/books/documents/book-user`

**权限**: 需要用户认证

### 1.13 VIP模块 (`/v1/vip`)

#### 获取VIP套餐列表
**GET** `/v1/vip`

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "name": "月度会员",
      "price": 99.00,
      "duration": 30,
      "description": "月度会员描述"
    }
  ]
}
```

### 1.14 订单模块 (`/v1/orders`)

#### 生成支付订单
**GET** `/v1/orders/pay`

**权限**: 需要用户认证

**查询参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| vip_id | int | 是 | VIP套餐ID |
| payment_method | string | 是 | 支付方式: alipay/wechatpay |

#### 生成续费订单
**GET** `/v1/orders/renew/pay`

**权限**: 需要用户认证

#### 支付宝回调
**GET** `/v1/orders/alipay/callback`

#### 支付宝通知
**POST** `/v1/orders/alipay/notify`

#### 微信支付回调
**GET** `/v1/orders/wechatpay/callback`

#### 微信支付通知
**POST** `/v1/orders/wechatpay/notify`

#### 获取用户订单信息
**GET** `/v1/orders/info`

**权限**: 需要用户认证

#### 获取用户订单列表
**GET** `/v1/orders/list`

**权限**: 需要用户认证

### 1.15 优惠券模块 (`/v1/user_coupon`)

#### 获取用户优惠券列表
**GET** `/v1/user_coupon`

**权限**: 需要用户认证

#### 用户抽奖获得优惠券
**POST** `/v1/user_coupon`

**权限**: 需要用户认证

### 1.16 文章模块 (`/v1/articles`)

#### 获取文章列表
**GET** `/v1/articles`

**权限**: 访客可访问，可选权限验证

**查询参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| keyword | string | 否 | 搜索关键词 |
| category_id | int | 否 | 分类ID |
| page | int | 否 | 页码 |
| per_page | int | 否 | 每页数量 |

#### 根据分类获取文章列表
**GET** `/v1/articles/category/{category_id}`

**权限**: 访客可访问，可选权限验证

#### 获取免费文章列表
**GET** `/v1/articles/public`

#### 获取付费文章列表
**GET** `/v1/articles/paid`

**权限**: 需要用户认证

#### 获取文章详情
**GET** `/v1/articles/{id}`

**权限**: 访客可访问，需要文章权限验证

#### 获取文章评论列表
**GET** `/v1/articles/{id}/comments`

#### 创建文章评论
**POST** `/v1/articles/{id}/comments`

**权限**: 需要用户认证

#### 获取单个评论详情
**GET** `/v1/articles/comments/{id}`

#### 更新评论
**PUT** `/v1/articles/comments/{id}`

**权限**: 需要用户认证

#### 删除评论
**DELETE** `/v1/articles/comments/{id}`

**权限**: 需要用户认证

### 1.17 文章分类模块 (`/v1/article-categories`)

#### 获取所有分类
**GET** `/v1/article-categories`

#### 获取单个分类
**GET** `/v1/article-categories/{id}`

### 1.18 安全验证模块 (`/v1/verification`)

**限流**: 每小时 50 次

#### 获取图形验证码
**GET** `/v1/verification/captcha`

#### 验证图形验证码
**POST** `/v1/verification/captcha/verify`

**权限**: 需要用户认证

#### 发送手机验证码
**POST** `/v1/verification/phone/send`

**权限**: 需要用户认证

#### 验证手机验证码
**POST** `/v1/verification/phone/verify`

**权限**: 需要用户认证

#### 获取验证状态
**GET** `/v1/verification/status`

**权限**: 需要用户认证

---

## 二、管理后台接口 (`/v1/backend`)

### 2.1 通用视频接口

#### 获取视频播放签名
**GET** `/v1/backend/videos/play-signature`

**权限**: 需要管理员认证

### 2.2 管理员模块 (`/v1/backend/admins`)

#### 创建管理员
**POST** `/v1/backend/admins`

**权限**: 需要管理员认证

#### 管理员登录
**POST** `/v1/backend/admins/login`

#### 管理员登出
**POST** `/v1/backend/admins/logout`

**权限**: 需要管理员认证

#### 刷新管理员Token
**POST** `/v1/backend/admins/refresh-token`

#### 更新管理员信息
**PUT** `/v1/backend/admins/{id}/update`

**权限**: 需要管理员认证

#### 重置管理员密码
**PUT** `/v1/backend/admins/{id}/reset-password`

**权限**: 需要管理员认证

#### 获取管理员列表
**GET** `/v1/backend/admins`

**权限**: 需要管理员认证

#### 删除管理员
**DELETE** `/v1/backend/admins/{id}`

**权限**: 需要管理员认证

#### 获取当前管理员信息
**GET** `/v1/backend/admins/userinfo`

**权限**: 需要管理员认证

### 2.3 用户管理模块 (`/v1/backend/users`)

#### 获取用户详情
**GET** `/v1/backend/users/{id}`

**权限**: 需要管理员认证

#### 获取用户列表
**GET** `/v1/backend/users`

**权限**: 需要管理员认证

**查询参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| keyword | string | 否 | 搜索关键词 |
| page | int | 否 | 页码 |
| per_page | int | 否 | 每页数量 |

#### 创建用户
**POST** `/v1/backend/users`

**权限**: 需要管理员认证

#### 更新用户信息
**PUT** `/v1/backend/users/{id}`

**权限**: 需要管理员认证

#### 根据手机号搜索用户
**GET** `/v1/backend/users/search-by-phone`

**权限**: 需要管理员认证

#### 删除用户
**DELETE** `/v1/backend/users/{id}`

**权限**: 需要管理员认证

### 2.4 课程管理模块 (`/v1/backend/courses`)

#### 获取课程列表
**GET** `/v1/backend/courses`

**权限**: 需要管理员认证

#### 获取所有课程
**GET** `/v1/backend/courses/all`

**权限**: 需要管理员认证

#### 获取课程详情
**GET** `/v1/backend/courses/{id}`

**权限**: 需要管理员认证

#### 创建课程
**POST** `/v1/backend/courses`

**权限**: 需要管理员认证

#### 更新课程
**PUT** `/v1/backend/courses/{id}`

**权限**: 需要管理员认证

#### 更新课程完成状态
**PUT** `/v1/backend/courses/complete/{id}`

**权限**: 需要管理员认证

#### 更新课程免费状态
**PUT** `/v1/backend/courses/is-free/{id}`

**权限**: 需要管理员认证

#### 删除课程
**DELETE** `/v1/backend/courses/{id}`

**权限**: 需要管理员认证

#### 更新课程排序
**PUT** `/v1/backend/courses/sort/{id}`

**权限**: 需要管理员认证

#### 设置课程讲师
**PUT** `/v1/backend/courses/instructor/{id}`

**权限**: 需要管理员认证

### 2.5 订单管理模块 (`/v1/backend/orders`)

#### 获取订单列表
**GET** `/v1/backend/orders`

**权限**: 需要管理员认证

#### 获取订单详情
**GET** `/v1/backend/orders/{id}`

**权限**: 需要管理员认证

#### 获取用户订单列表
**GET** `/v1/backend/orders/user/{id}`

**权限**: 需要管理员认证

#### 更新订单
**PUT** `/v1/backend/orders/{id}`

**权限**: 需要管理员认证

#### 删除订单
**DELETE** `/v1/backend/orders/{id}`

**权限**: 需要管理员认证

### 2.6 分销商管理模块 (`/v1/backend/distributors`)

#### 获取分销商列表
**GET** `/v1/backend/distributors`

**权限**: 需要管理员认证

#### 创建分销商
**POST** `/v1/backend/distributors`

**权限**: 需要管理员认证

#### 更新分销商
**PUT** `/v1/backend/distributors/{id}`

**权限**: 需要管理员认证

#### 删除分销商
**DELETE** `/v1/backend/distributors/{id}`

**权限**: 需要管理员认证

### 2.7 文章管理模块 (`/v1/backend/articles`)

#### 获取文章列表
**GET** `/v1/backend/articles`

**权限**: 需要管理员认证

#### 获取文章详情
**GET** `/v1/backend/articles/{id}`

**权限**: 需要管理员认证

#### 根据分类获取文章列表
**GET** `/v1/backend/articles/category/{category_id}`

**权限**: 需要管理员认证

#### 获取免费文章列表
**GET** `/v1/backend/articles/public`

**权限**: 需要管理员认证

#### 获取付费文章列表
**GET** `/v1/backend/articles/paid`

**权限**: 需要管理员认证

---

## 三、分销商接口 (`/v1/distributor`)

### 3.1 分销商认证

#### 发送手机验证码
**POST** `/v1/distributor/verify-codes/phone`

**限流**: 每小时 15 次

#### 分销商注册
**POST** `/v1/distributor/signup`

#### 检查手机号是否存在
**POST** `/v1/distributor/is_phone_exist`

#### 检查用户名是否存在
**POST** `/v1/distributor/is_name_exist`

#### 手机号登录
**POST** `/v1/distributor/login/using-phone`

**权限**: 访客可访问

#### 密码登录
**POST** `/v1/distributor/login/using-password`

**权限**: 访客可访问

#### 获取当前分销商信息
**GET** `/v1/distributor/userinfo`

**权限**: 需要分销商认证

### 3.2 网站设置模块 (`/v1/distributor/website-settings`)

#### 获取网站设置
**GET** `/v1/distributor/website-settings`

**权限**: 需要分销商认证

#### 更新网站设置
**PUT** `/v1/distributor/website-settings`

**权限**: 需要分销商认证

### 3.3 分销商用户模块 (`/v1/distributor/users`)

#### 获取分销商用户列表
**GET** `/v1/distributor/users`

**权限**: 需要分销商认证

#### 添加分销商用户
**POST** `/v1/distributor/users`

**权限**: 需要分销商认证

#### 删除分销商用户
**DELETE** `/v1/distributor/users/{id}`

**权限**: 需要分销商认证

#### 检查用户是否存在
**GET** `/v1/distributor/users/is_phone_exist`

**权限**: 需要分销商认证

#### 获取用户统计
**GET** `/v1/distributor/users/statistics`

**权限**: 需要分销商认证

### 3.4 运营模块 (`/v1/distributor/operation`)

#### 生成分销商订单
**GET** `/v1/distributor/operation/orders/pay`

**权限**: 需要分销商认证

#### 支付宝回调
**GET** `/v1/distributor/operation/orders/alipay/callback`

#### 支付宝通知
**POST** `/v1/distributor/operation/orders/alipay/notify`

#### 获取分销商订单列表
**GET** `/v1/distributor/operation/orders/{id}`

**权限**: 需要分销商认证

---

## 四、讲师接口 (`/v1/instructor`)

### 4.1 讲师认证

#### 发送手机验证码
**POST** `/v1/instructor/verify-codes/phone`

**限流**: 每小时 15 次

#### 手机号登录
**POST** `/v1/instructor/login/using-phone`

**权限**: 访客可访问

#### 获取当前讲师信息
**GET** `/v1/instructor/userinfo`

**权限**: 需要讲师认证

### 4.2 讲师课程管理 (`/v1/instructor/courses`)

#### 获取当前讲师的课程列表
**GET** `/v1/instructor/courses`

**权限**: 需要讲师认证

---

## 五、错误码说明

### HTTP 状态码
- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `422`: 表单验证失败
- `429`: 请求过于频繁
- `500`: 服务器内部错误

### 业务错误码
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "表单验证失败",
    "details": {
      "phone": ["手机号格式不正确"]
    }
  }
}
```

## 六、数据格式说明

### 分页响应格式
```json
{
  "data": [],
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "last_page": 7
  },
  "links": {
    "first": "http://example.com/api?page=1",
    "last": "http://example.com/api?page=7",
    "prev": null,
    "next": "http://example.com/api?page=2"
  }
}
```

### 时间格式
所有时间字段均使用 ISO 8601 格式：`2025-07-30T10:00:00Z`

### 文件上传
支持的图片格式：`jpg`, `jpeg`, `png`, `gif`
最大文件大小：`5MB`

---

## 七、版本更新记录

### v1.0.0 (2025-07-30)
- 初始版本发布
- 包含用户认证、课程管理、订单支付、分销商管理等核心功能
- 支持前端用户、管理后台、分销商、讲师四个端的API接口

---

*本文档最后更新时间：2025-07-30*
