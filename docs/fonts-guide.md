# 开源字体使用指南

## 🎯 当前配置说明

项目已配置为使用完全开源、无版权风险的字体方案。

## 📋 推荐的开源字体

### 中文字体

#### 1. **思源黑体 (Source Han Sans)**
- **开源协议**: SIL Open Font License 1.1
- **开发者**: Adobe
- **特点**: 支持简体中文、繁体中文、日文、韩文
- **下载**: https://github.com/adobe-fonts/source-han-sans
- **商用**: ✅ 完全免费商用

#### 2. **思源宋体 (Source Han Serif)**
- **开源协议**: SIL Open Font License 1.1
- **开发者**: Adobe
- **特点**: 衬线字体，适合正文阅读
- **下载**: https://github.com/adobe-fonts/source-han-serif
- **商用**: ✅ 完全免费商用

#### 3. **Noto Sans CJK**
- **开源协议**: SIL Open Font License 1.1
- **开发者**: Google
- **特点**: Google 的开源中日韩字体
- **下载**: https://github.com/googlefonts/noto-cjk
- **商用**: ✅ 完全免费商用

### 英文字体

#### 1. **Inter**
- **开源协议**: SIL Open Font License 1.1
- **开发者**: Rasmus Andersson
- **特点**: 现代、清晰的无衬线字体
- **下载**: https://github.com/rsms/inter
- **商用**: ✅ 完全免费商用

#### 2. **Roboto**
- **开源协议**: Apache License 2.0
- **开发者**: Google
- **特点**: Android 系统默认字体
- **下载**: https://github.com/googlefonts/roboto
- **商用**: ✅ 完全免费商用

### 等宽字体

#### 1. **JetBrains Mono**
- **开源协议**: SIL Open Font License 1.1
- **开发者**: JetBrains
- **特点**: 专为开发者设计，支持连字
- **下载**: https://github.com/JetBrains/JetBrainsMono
- **商用**: ✅ 完全免费商用

#### 2. **Fira Code**
- **开源协议**: SIL Open Font License 1.1
- **开发者**: Mozilla
- **特点**: 支持编程连字符
- **下载**: https://github.com/tonsky/FiraCode
- **商用**: ✅ 完全免费商用

## 🚀 快速使用方案

### 方案一：使用 CDN（推荐）

在 `app.vue` 中添加：

```vue
<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>

<script setup>
// 使用开源字体 CDN
useHead({
  link: [
    // 思源黑体
    {
      rel: 'stylesheet',
      href: 'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap'
    },
    // Inter 字体
    {
      rel: 'stylesheet', 
      href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
    },
    // JetBrains Mono
    {
      rel: 'stylesheet',
      href: 'https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap'
    }
  ]
})
</script>
```

### 方案二：本地字体文件

1. 创建 `public/fonts` 目录
2. 下载字体文件并放入该目录
3. 在 `nuxt.config.ts` 中取消注释相应配置

## ⚖️ 版权对比

| 字体名称 | 版权状态 | 商用风险 | 推荐度 |
|---------|---------|---------|--------|
| PingFang SC | Apple 专有 | ⚠️ 高风险 | ❌ 不推荐 |
| Microsoft YaHei | 微软专有 | ⚠️ 高风险 | ❌ 不推荐 |
| Source Han Sans | Adobe 开源 | ✅ 无风险 | ⭐⭐⭐⭐⭐ |
| Noto Sans CJK | Google 开源 | ✅ 无风险 | ⭐⭐⭐⭐⭐ |
| Inter | 开源 | ✅ 无风险 | ⭐⭐⭐⭐⭐ |
| Roboto | Google 开源 | ✅ 无风险 | ⭐⭐⭐⭐ |

## 🔧 自定义配置

如需使用特定的开源字体，请修改 `nuxt.config.ts` 中的 `families` 配置。

## 📞 技术支持

如有字体相关问题，请参考各字体的官方文档或 GitHub 仓库。
