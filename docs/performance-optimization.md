# Nuxt 项目性能优化指南

## 热更新优化方案

### 1. 开发服务器配置优化

```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  vite: {
    server: {
      hmr: {
        port: 24678, // 固定HMR端口
        overlay: false, // 关闭错误覆盖层
        clientPort: 24678
      },
      watch: {
        usePolling: false, // 使用原生文件监听
        ignored: ['**/node_modules/**', '**/.git/**']
      }
    }
  }
})
```

### 2. 实验性功能优化

```typescript
experimental: {
  payloadExtraction: false, // 禁用payload提取
  inlineSSRStyles: false, // 开发环境禁用内联样式
  typedPages: false // 关闭类型化页面
}
```

### 3. 依赖预构建优化

```typescript
vite: {
  optimizeDeps: {
    include: [
      'naive-ui',
      'vueuc',
      '@css-render/vue3-ssr',
      '@juggle/resize-observer',
      'vue',
      'vue-router'
    ],
    exclude: ['@nuxt/devtools']
  }
}
```

## 样式加载优化方案

### 1. CSS 处理优化

```typescript
vite: {
  css: {
    devSourcemap: true, // 开发环境启用sourcemap
    preprocessorOptions: {
      scss: {
        additionalData: `@use "sass:math";`,
        charset: false
      }
    }
  }
}
```

### 2. 字体加载优化

- 使用本地字体文件避免网络请求
- 配置字体预加载
- 设置合理的字体回退方案

```typescript
fonts: {
  providers: {
    google: false, // 禁用Google Fonts
    googleicons: false
  },
  families: [
    {
      name: 'Inter',
      src: '/fonts/Inter-Regular.woff2',
      weight: '400',
      style: 'normal'
    }
  ]
}
```

### 3. 组件库优化

```typescript
build: {
  transpile: ['naive-ui', 'vueuc']
}
```

## 使用方法

### 开发环境

1. **使用优化配置启动**：
   ```bash
   # 使用开发优化配置
   npx nuxi dev --config nuxt.config.dev.ts
   ```

2. **或者修改package.json**：
   ```json
   {
     "scripts": {
       "dev": "nuxi dev --config nuxt.config.dev.ts",
       "dev:fast": "nuxi dev --config nuxt.config.dev.ts --no-clear"
     }
   }
   ```

### 生产环境

保持使用原始配置文件：
```bash
npm run build
```

## 额外优化建议

### 1. 开发工具优化

- 关闭不必要的浏览器扩展
- 使用性能更好的终端（如 iTerm2）
- 确保有足够的内存和CPU资源

### 2. 项目结构优化

- 减少深层嵌套的组件
- 合理使用动态导入
- 避免在开发环境使用重型分析工具

### 3. 缓存优化

```typescript
vite: {
  build: {
    rollupOptions: {
      output: {
        manualChunks: undefined // 开发环境不分包
      }
    }
  }
}
```

### 4. 监听文件优化

```typescript
vite: {
  server: {
    watch: {
      ignored: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/.nuxt/**'
      ]
    }
  }
}
```

## 性能监控

### 1. 开发服务器启动时间

观察控制台输出的启动时间，优化后应该有明显改善。

### 2. 热更新响应时间

修改文件后观察浏览器更新的延迟时间。

### 3. 样式加载时间

使用浏览器开发者工具的Network面板监控CSS文件加载时间。

## 故障排除

### 1. 热更新不工作

- 检查HMR端口是否被占用
- 确认文件监听配置正确
- 重启开发服务器

### 2. 样式不更新

- 清除浏览器缓存
- 检查CSS sourcemap配置
- 确认预处理器配置正确

### 3. 内存占用过高

- 减少optimizeDeps.include的依赖
- 关闭不必要的devtools
- 增加ignored文件配置

通过以上优化方案，Nuxt项目的开发体验应该会有显著提升。建议根据项目实际情况逐步应用这些优化配置。