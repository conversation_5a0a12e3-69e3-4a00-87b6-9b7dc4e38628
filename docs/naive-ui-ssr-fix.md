# NaiveUI SSR 样式闪烁问题解决方案

## 问题描述

在使用 NaiveUI 与 Nuxt.js SSR 时，会出现以下问题：

1. 页面刷新时 NaiveUI 组件样式闪烁（FOUC - Flash of Unstyled Content）
2. 组件在客户端水合前显示无样式状态
3. 样式加载延迟 3-4 秒

## 解决方案

### 1. 使用官方模块

确保使用官方的 `nuxtjs-naive-ui` 模块：

```bash
pnpm add nuxtjs-naive-ui
```

### 2. 正确的 Nuxt 配置

在 `nuxt.config.ts` 中：

```typescript
export default defineNuxtConfig({
  modules: ["nuxtjs-naive-ui"],

  // 构建配置 - 解决 vueuc 兼容性问题
  build: {
    transpile: ["naive-ui", "vueuc", "date-fns"],
  },

  vite: {
    // SSR 配置 - 解决样式闪烁问题
    ssr: {
      noExternal: ["naive-ui", "vueuc", "date-fns"],
    },
    // 优化依赖预构建
    optimizeDeps: {
      include: ["naive-ui", "vueuc", "date-fns"],
    },
  },
});
```

### 3. 简化的 App.vue

```vue
<template>
  <div id="app">
    <n-config-provider :theme="theme">
      <n-global-style />
      <n-loading-bar-provider>
        <n-dialog-provider>
          <n-notification-provider>
            <n-message-provider>
              <NuxtLayout>
                <NuxtPage />
              </NuxtLayout>
            </n-message-provider>
          </n-notification-provider>
        </n-dialog-provider>
      </n-loading-bar-provider>
    </n-config-provider>
  </div>
</template>
```

### 4. 必要的依赖

确保安装正确版本的依赖：

```json
{
  "dependencies": {
    "naive-ui": "^2.42.0",
    "nuxtjs-naive-ui": "^1.0.2"
  },
  "devDependencies": {
    "@css-render/vue3-ssr": "^0.15.14"
  }
}
```

## 关键点

1. **不要手动配置** `unplugin-vue-components` 和 `unplugin-auto-import`，官方模块会自动处理
2. **必须保留** `build.transpile` 配置来解决 vueuc 兼容性问题
3. **必须配置** `vite.ssr.noExternal` 来确保 SSR 正常工作
4. **版本要求** `@css-render/vue3-ssr` 版本必须 >= 0.15.14

## 验证方法

1. 访问 `/test-naive` 页面查看 NaiveUI 组件是否正常显示
2. 刷新页面检查是否还有样式闪烁
3. 查看网络面板确认样式文件正确加载

## 当前项目解决方案（已实施）

### 手动配置 SSR 样式渲染

我们采用了手动配置方案，通过以下步骤解决了样式闪烁问题：

#### 1. 创建 SSR 样式渲染插件

文件：`plugins/naive-ssr.server.ts`

- 使用 `@css-render/vue3-ssr` 收集服务端样式
- 自动注入样式到 HTML 头部
- 确保客户端水合时样式已存在

#### 2. 优化 Nuxt 配置

在 `nuxt.config.ts` 中添加：

```typescript
// SSR 配置
ssr: {
  noExternal: ['naive-ui', 'vueuc', 'date-fns', '@css-render/vue3-ssr']
},

// 构建配置
build: {
  transpile: ['naive-ui', 'vueuc', 'date-fns', '@css-render/vue3-ssr']
},

// 优化依赖预构建
optimizeDeps: {
  include: ['naive-ui', 'vueuc', 'date-fns', '@css-render/vue3-ssr']
}
```

#### 3. 简化 app.vue

移除了不必要的 meta 标签，样式注入现在由插件自动处理。

### 优势

- **轻量级**：只添加了一个小插件文件（约 30 行代码）
- **保持架构**：不改变现有项目结构
- **自动化**：无需手动管理样式注入
- **兼容性**：与现有配置完全兼容

## 故障排除

如果仍有问题：

1. 清除缓存：`rm -rf .nuxt dist .output node_modules/.cache`
2. 重新安装依赖：`pnpm install`
3. 检查浏览器控制台是否有错误信息
4. 确认所有依赖版本符合要求
5. 验证 `plugins/naive-ssr.server.ts` 文件是否正确创建
