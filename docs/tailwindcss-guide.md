# TailwindCSS 使用指南

## 目录

- [基础概念](#基础概念)
- [布局系统](#布局系统)
- [响应式设计](#响应式设计)
- [颜色系统](#颜色系统)
- [字体排版](#字体排版)
- [间距系统](#间距系统)
- [边框与圆角](#边框与圆角)
- [阴影效果](#阴影效果)
- [动画与过渡](#动画与过渡)
- [常用组件示例](#常用组件示例)
- [自定义配置](#自定义配置)

## 基础概念

TailwindCSS 是一个功能优先的 CSS 框架，通过组合小的工具类来构建复杂的设计。

### 核心原则

- **功能优先**: 每个类都有单一的功能
- **响应式**: 内置响应式设计支持
- **可定制**: 高度可配置的设计系统
- **性能优化**: 只包含使用的样式

## 布局系统

### Flexbox 布局

```html
<!-- 基础 Flex 容器 -->
<div class="flex">
  <div>项目 1</div>
  <div>项目 2</div>
</div>

<!-- 常用 Flex 组合 -->
<div class="flex items-center justify-between">
  <div>左侧内容</div>
  <div>右侧内容</div>
</div>

<!-- 垂直居中 -->
<div class="flex items-center justify-center h-screen">
  <div>居中内容</div>
</div>

<!-- Flex 方向 -->
<div class="flex flex-col">
  <!-- 垂直排列 -->
  <div class="flex flex-row">
    <!-- 水平排列（默认） -->
    <div class="flex flex-col-reverse"><!-- 垂直反向 --></div>
  </div>
</div>
```

### Grid 布局

```html
<!-- 基础网格 -->
<div class="grid grid-cols-3 gap-4">
  <div>网格项 1</div>
  <div>网格项 2</div>
  <div>网格项 3</div>
</div>

<!-- 响应式网格 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <!-- 移动端1列，平板2列，桌面3列 -->
</div>

<!-- 复杂网格布局 -->
<div class="grid grid-cols-4 grid-rows-3 gap-4">
  <div class="col-span-2 row-span-2">大项目</div>
  <div class="col-span-1">小项目</div>
</div>
```

### 定位系统

```html
<!-- 相对定位 -->
<div class="relative">
  <div class="absolute top-0 right-0">绝对定位元素</div>
</div>

<!-- 固定定位 -->
<div class="fixed top-4 right-4">固定在右上角</div>

<!-- 粘性定位 -->
<div class="sticky top-0">粘性导航栏</div>
```

## 响应式设计

### 断点系统

- `sm`: 640px 及以上
- `md`: 768px 及以上
- `lg`: 1024px 及以上
- `xl`: 1280px 及以上
- `2xl`: 1536px 及以上

### 响应式用法

```html
<!-- 响应式文字大小 -->
<h1 class="text-2xl md:text-4xl lg:text-6xl">响应式标题</h1>

<!-- 响应式间距 -->
<div class="p-4 md:p-8 lg:p-12">响应式内边距</div>

<!-- 响应式显示/隐藏 -->
<div class="block md:hidden">移动端显示</div>
<div class="hidden md:block">桌面端显示</div>

<!-- 响应式布局 -->
<div class="flex flex-col md:flex-row">移动端垂直，桌面端水平</div>
```

## 颜色系统

### 基础颜色

```html
<!-- 文字颜色 -->
<p class="text-gray-900">深灰色文字</p>
<p class="text-blue-600">蓝色文字</p>
<p class="text-red-500">红色文字</p>

<!-- 背景颜色 -->
<div class="bg-white">白色背景</div>
<div class="bg-gray-100">浅灰背景</div>
<div class="bg-blue-500">蓝色背景</div>

<!-- 边框颜色 -->
<div class="border border-gray-300">灰色边框</div>
<div class="border-2 border-blue-500">蓝色粗边框</div>
```

### 自定义颜色（项目配置）

```html
<!-- 使用自定义主色调 -->
<button class="bg-primary-600 text-white">主要按钮</button>
<div class="text-secondary-700">次要文字</div>
```

## 字体排版

### 字体大小

```html
<p class="text-xs">极小文字 (12px)</p>
<p class="text-sm">小文字 (14px)</p>
<p class="text-base">基础文字 (16px)</p>
<p class="text-lg">大文字 (18px)</p>
<p class="text-xl">特大文字 (20px)</p>
<p class="text-2xl">超大文字 (24px)</p>
<p class="text-4xl">巨大文字 (36px)</p>
```

### 字体粗细

```html
<p class="font-thin">极细 (100)</p>
<p class="font-light">细 (300)</p>
<p class="font-normal">正常 (400)</p>
<p class="font-medium">中等 (500)</p>
<p class="font-semibold">半粗 (600)</p>
<p class="font-bold">粗 (700)</p>
<p class="font-black">极粗 (900)</p>
```

### 行高与字间距

```html
<p class="leading-tight">紧密行高</p>
<p class="leading-normal">正常行高</p>
<p class="leading-loose">宽松行高</p>

<p class="tracking-tight">紧密字间距</p>
<p class="tracking-normal">正常字间距</p>
<p class="tracking-wide">宽松字间距</p>
```

### 文本对齐

```html
<p class="text-left">左对齐</p>
<p class="text-center">居中对齐</p>
<p class="text-right">右对齐</p>
<p class="text-justify">两端对齐</p>
```

## 间距系统

### 内边距 (Padding)

```html
<div class="p-4">四周内边距</div>
<div class="px-4 py-2">水平4，垂直2</div>
<div class="pt-4 pr-2 pb-4 pl-2">分别设置</div>
```

### 外边距 (Margin)

```html
<div class="m-4">四周外边距</div>
<div class="mx-auto">水平居中</div>
<div class="mt-8 mb-4">上8下4</div>
<div class="-mt-4">负边距</div>
```

### 间距数值对照

- `0`: 0px
- `1`: 4px
- `2`: 8px
- `4`: 16px
- `8`: 32px
- `16`: 64px
- `32`: 128px

## 边框与圆角

### 边框

```html
<!-- 边框宽度 -->
<div class="border">1px 边框</div>
<div class="border-2">2px 边框</div>
<div class="border-4">4px 边框</div>

<!-- 边框样式 -->
<div class="border border-solid">实线边框</div>
<div class="border border-dashed">虚线边框</div>
<div class="border border-dotted">点线边框</div>

<!-- 单边边框 -->
<div class="border-t">上边框</div>
<div class="border-r">右边框</div>
<div class="border-b">下边框</div>
<div class="border-l">左边框</div>
```

### 圆角

```html
<div class="rounded">小圆角</div>
<div class="rounded-md">中等圆角</div>
<div class="rounded-lg">大圆角</div>
<div class="rounded-full">完全圆形</div>

<!-- 单角圆角 -->
<div class="rounded-t-lg">上方圆角</div>
<div class="rounded-tr-lg">右上圆角</div>
```

## 阴影效果

### 盒子阴影

```html
<div class="shadow-sm">小阴影</div>
<div class="shadow">默认阴影</div>
<div class="shadow-md">中等阴影</div>
<div class="shadow-lg">大阴影</div>
<div class="shadow-xl">特大阴影</div>
<div class="shadow-2xl">超大阴影</div>
<div class="shadow-none">无阴影</div>

<!-- 内阴影 -->
<div class="shadow-inner">内阴影</div>

<!-- 彩色阴影 -->
<div class="shadow-lg shadow-blue-500/50">蓝色阴影</div>
```

## 动画与过渡

### 过渡效果

```html
<!-- 基础过渡 -->
<button class="transition hover:bg-blue-500">悬停变色</button>

<!-- 指定过渡属性 -->
<div class="transition-colors duration-300 hover:bg-red-500">颜色过渡</div>

<div class="transition-transform duration-500 hover:scale-110">缩放过渡</div>

<!-- 过渡时长 -->
<div class="transition duration-75">75ms</div>
<div class="transition duration-300">300ms</div>
<div class="transition duration-700">700ms</div>
```

### 变换效果

```html
<!-- 缩放 -->
<div class="transform scale-75">缩小</div>
<div class="transform scale-110">放大</div>
<div class="hover:scale-105 transition">悬停放大</div>

<!-- 旋转 -->
<div class="transform rotate-45">旋转45度</div>
<div class="transform -rotate-12">反向旋转</div>

<!-- 平移 -->
<div class="transform translate-x-4">右移</div>
<div class="transform -translate-y-2">上移</div>
```

### 内置动画

```html
<div class="animate-spin">旋转动画</div>
<div class="animate-ping">脉冲动画</div>
<div class="animate-pulse">呼吸动画</div>
<div class="animate-bounce">弹跳动画</div>

<!-- 自定义动画（在配置中定义） -->
<div class="animate-fade-in">淡入动画</div>
<div class="animate-slide-up">上滑动画</div>
```

## 常用组件示例

### 按钮组件

```html
<!-- 主要按钮 -->
<button class="btn-primary">主要操作</button>

<!-- 次要按钮 -->
<button class="btn-secondary">次要操作</button>

<!-- 轮廓按钮 -->
<button class="btn-outline">轮廓按钮</button>

<!-- 自定义按钮 -->
<button
  class="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition duration-200"
>
  渐变按钮
</button>
```

### 卡片组件

```html
<div class="card max-w-sm mx-auto">
  <div class="card-header">
    <h3 class="text-lg font-semibold">卡片标题</h3>
  </div>
  <div class="card-body">
    <p class="text-gray-600">卡片内容描述...</p>
  </div>
  <div class="card-footer">
    <button class="btn-primary">操作按钮</button>
  </div>
</div>
```

### 导航栏

```html
<nav class="bg-white shadow-lg">
  <div class="max-w-7xl mx-auto px-4">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex-shrink-0">
        <img class="h-8 w-8" src="/logo.svg" alt="Logo" />
      </div>

      <!-- 导航链接 -->
      <div class="hidden md:block">
        <div class="ml-10 flex items-baseline space-x-4">
          <a
            href="#"
            class="text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition"
          >
            首页
          </a>
          <a
            href="#"
            class="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition"
          >
            关于
          </a>
          <a
            href="#"
            class="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition"
          >
            联系
          </a>
        </div>
      </div>

      <!-- 移动端菜单按钮 -->
      <div class="md:hidden">
        <button class="text-gray-500 hover:text-gray-900 focus:outline-none">
          <svg
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</nav>
```

### 表单组件

```html
<form class="max-w-md mx-auto space-y-6">
  <!-- 输入框 -->
  <div>
    <label class="block text-sm font-medium text-gray-700 mb-2">
      邮箱地址
    </label>
    <input type="email" class="input" placeholder="请输入邮箱" />
  </div>

  <!-- 密码框 -->
  <div>
    <label class="block text-sm font-medium text-gray-700 mb-2"> 密码 </label>
    <input type="password" class="input" placeholder="请输入密码" />
  </div>

  <!-- 选择框 -->
  <div>
    <label class="block text-sm font-medium text-gray-700 mb-2">
      国家/地区
    </label>
    <select class="input">
      <option>中国</option>
      <option>美国</option>
      <option>日本</option>
    </select>
  </div>

  <!-- 复选框 -->
  <div class="flex items-center">
    <input
      type="checkbox"
      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
    />
    <label class="ml-2 block text-sm text-gray-900"> 记住我 </label>
  </div>

  <!-- 提交按钮 -->
  <button type="submit" class="w-full btn-primary">登录</button>
</form>
```

### 模态框

```html
<!-- 模态框背景 -->
<div
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
>
  <!-- 模态框内容 -->
  <div
    class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all"
  >
    <!-- 模态框头部 -->
    <div class="flex items-center justify-between p-6 border-b">
      <h3 class="text-lg font-semibold">确认操作</h3>
      <button class="text-gray-400 hover:text-gray-600">
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>
    </div>

    <!-- 模态框内容 -->
    <div class="p-6">
      <p class="text-gray-600">您确定要执行此操作吗？此操作无法撤销。</p>
    </div>

    <!-- 模态框底部 -->
    <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50">
      <button class="btn-outline">取消</button>
      <button class="btn-primary">确认</button>
    </div>
  </div>
</div>
```

## 自定义配置

### 扩展主题

在 `tailwind.config.js` 中可以扩展默认主题：

```javascript
module.exports = {
  theme: {
    extend: {
      // 自定义颜色
      colors: {
        brand: {
          50: "#eff6ff",
          500: "#3b82f6",
          900: "#1e3a8a",
        },
      },
      // 自定义字体
      fontFamily: {
        custom: ["Custom Font", "sans-serif"],
      },
      // 自定义间距
      spacing: {
        72: "18rem",
        84: "21rem",
        96: "24rem",
      },
    },
  },
};
```

### 自定义组件类

在 CSS 文件中使用 `@layer` 指令：

```css
@layer components {
  .btn-custom {
    @apply px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition;
  }
}
```

### 常用工具类组合

```html
<!-- 居中容器 -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <!-- 响应式网格 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- 卡片悬停效果 -->
    <div
      class="bg-white rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-1 transition duration-300"
    >
      <!-- 渐变背景 -->
      <div class="bg-gradient-to-r from-blue-500 to-purple-600">
        <!-- 文字渐变 -->
        <h1
          class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
        >
          <!-- 玻璃效果 -->
          <div
            class="glass backdrop-blur-md bg-white/30 border border-white/20"
          ></div>
        </h1>
      </div>
    </div>
  </div>
</div>
```

## 最佳实践

1. **组件化思维**: 将常用的样式组合封装成组件类
2. **响应式优先**: 始终考虑移动端体验
3. **性能优化**: 使用 PurgeCSS 移除未使用的样式
4. **语义化**: 结合语义化的 HTML 结构
5. **可维护性**: 建立一致的设计系统和命名规范

## 调试技巧

1. **浏览器开发者工具**: 查看应用的具体样式
2. **Tailwind CSS IntelliSense**: VS Code 插件提供自动补全
3. **官方文档**: https://tailwindcss.com/docs
4. **在线工具**: Tailwind Play (https://play.tailwindcss.com/)

---

这份指南涵盖了 TailwindCSS 的核心用法，更多高级特性请参考官方文档。
