# Stagewise 集成指南

## 问题分析

您遇到的问题是 Stagewise 工具栏无法检测到智能体，主要原因如下：

1. **端口扫描范围不匹配**：Stagewise 默认从端口 5746 开始扫描，但您的后端运行在 3008
2. **缺少必需端点**：后端缺少 `/stagewise/info` 和 `/stagewise/ws` 端点
3. **VSCode 扩展通信**：需要正确的通信协议

## 解决方案

### 方案一：修改前端配置（已完成）

已在 `app.vue` 中启用了 Stagewise 的实验性功能：

```javascript
const stagewiseConfig = {
  plugins: [VuePlugin],
  experimental: {
    enableStagewiseMCP: true,
    enableToolCalls: true
  }
};
```

### 方案二：后端添加 Stagewise 端点

在您的后端服务中添加以下端点：

#### 1. 信息端点 `/stagewise/info`

```javascript
// 返回智能体信息
app.get('/stagewise/info', (req, res) => {
  res.json({
    name: "牛码城智能体",
    description: "牛码城平台的AI助手",
    capabilities: {
      chat: true,
      codeGeneration: true,
      fileOperations: true
    },
    version: "1.0.0"
  });
});
```

#### 2. WebSocket 端点 `/stagewise/ws`

```javascript
// WebSocket 连接用于实时通信
const WebSocket = require('ws');
const wss = new WebSocket.Server({ 
  port: 3008, 
  path: '/stagewise/ws' 
});

wss.on('connection', (ws) => {
  console.log('Stagewise client connected');
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      // 处理来自 VSCode 的消息
      handleStagewiseMessage(data, ws);
    } catch (error) {
      console.error('Error parsing message:', error);
    }
  });
  
  ws.on('close', () => {
    console.log('Stagewise client disconnected');
  });
});
```

### 方案三：使用代理配置

在 `nuxt.config.ts` 中添加 Stagewise 代理：

```javascript
proxy: {
  '/api': {
    target: 'http://127.0.0.1:3008',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/api/, '')
  },
  '/stagewise': {
    target: 'http://127.0.0.1:5746', // 或您的智能体端口
    changeOrigin: true,
    ws: true // 支持 WebSocket
  }
}
```

## 测试步骤

1. **检查端口**：确认后端服务运行在 3008 端口
2. **测试端点**：访问 `http://localhost:3008/stagewise/info`
3. **检查 VSCode 扩展**：确保 Stagewise VSCode 扩展已安装并激活
4. **刷新工具栏**：在 Stagewise 工具栏中点击刷新按钮

## 调试建议

1. **查看浏览器控制台**：检查是否有网络错误
2. **检查网络请求**：在开发者工具中查看对 `/stagewise/info` 的请求
3. **验证 WebSocket 连接**：确认 WebSocket 连接是否成功建立

## 常见问题

### Q: 工具栏显示"No agents found"
A: 检查后端是否提供了 `/stagewise/info` 端点，并返回正确的 JSON 格式

### Q: VSCode 扩展无法连接
A: 确保 VSCode 中的 Stagewise 扩展已激活，并检查端口配置

### Q: WebSocket 连接失败
A: 检查防火墙设置和端口是否被占用

## 下一步

1. 实现后端 Stagewise 端点
2. 测试 VSCode 扩展连接
3. 验证消息传递功能
