<template>
  <div class="footer-container">
    <div class="footer-grid">
      <!-- Logo and description -->
      <div class="footer-col-main">
        <div class="logo-section">
          <div class="logo-container">
            <div class="logo-icon">
              <span class="logo-text">牛</span>
            </div>
            <span class="logo-name">牛马学城</span>
          </div>
          <p class="footer-description">
            专注前端开发技术分享的在线学习平台，提供高质量的编程课程和技术文章，助力你的技术成长之路。
          </p>
          <div class="social-links">
            <a href="#" class="social-link">
              <span class="sr-only">Twitter</span>
              <svg class="social-icon" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                />
              </svg>
            </a>
            <a href="#" class="social-link">
              <span class="sr-only">GitHub</span>
              <svg class="social-icon" fill="currentColor" viewBox="0 0 24 24">
                <path
                  fill-rule="evenodd"
                  d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                  clip-rule="evenodd"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Navigation links -->
      <div class="footer-col">
        <h3 class="footer-heading">产品</h3>
        <ul class="footer-links">
          <li>
            <a href="#" class="footer-link">课程</a>
          </li>
          <li>
            <a href="#" class="footer-link">文章</a>
          </li>
          <li>
            <a href="#" class="footer-link">会员</a>
          </li>
          <li>
            <a href="#" class="footer-link">社区</a>
          </li>
        </ul>
      </div>

      <div class="footer-col">
        <h3 class="footer-heading">支持</h3>
        <ul class="footer-links">
          <li>
            <a href="#" class="footer-link">帮助中心</a>
          </li>
          <li>
            <a href="#" class="footer-link">联系我们</a>
          </li>
          <li>
            <a href="#" class="footer-link">隐私政策</a>
          </li>
          <li>
            <a href="#" class="footer-link">使用条款</a>
          </li>
        </ul>
      </div>

      <!-- 友情链接 -->
      <div class="footer-col">
        <h3 class="footer-heading">友情链接</h3>
        <ul class="footer-links">
          <li v-for="link in links" :key="link.id">
            <a
              :href="link.url"
              class="footer-link"
              target="_blank"
              rel="noopener noreferrer"
            >
              {{ link.name }}
            </a>
          </li>
        </ul>
      </div>
    </div>

    <div class="footer-divider"></div>

    <div class="footer-copyright">
      <p class="copyright-text">
        &copy; {{ new Date().getFullYear() }} 牛马学城. 保留所有权利。
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getLinks } from "@/api/links";

// 友情链接数据
const links = ref([]);
const loading = ref(false);

// 获取友情链接
const fetchLinks = async () => {
  try {
    loading.value = true;
    const response = await getLinks();
    links.value = response.data || [];
  } catch (error) {
    console.error("获取友情链接失败:", error);
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取友情链接
onMounted(() => {
  fetchLinks();
});
</script>

<style scoped>
.footer-container {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .footer-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

.footer-col-main {
  grid-column: span 1 / span 1;
}

@media (min-width: 768px) {
  .footer-col-main {
    grid-column: span 2 / span 2;
  }
}

.logo-section {
  margin-bottom: 1.5rem;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.logo-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  background-color: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  color: #ffffff;
  font-weight: bold;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.logo-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #111827);
}

.footer-description {
  color: var(--text-secondary, #4b5563);
  margin-bottom: 1.5rem;
  max-width: 28rem;
  line-height: 1.625;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  color: var(--text-secondary, #6b7280);
  transition: color 0.2s;
}

.social-link:hover {
  color: var(--text-color, #374151);
}

.social-icon {
  height: 1.5rem;
  width: 1.5rem;
}

.footer-col {
  grid-column: span 1 / span 1;
}

.footer-heading {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-primary, #111827);
  margin-bottom: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-link {
  color: var(--text-secondary, #4b5563);
  text-decoration: none;
  transition: color 0.2s;
  line-height: 2;
  display: block;
}

.footer-link:hover {
  color: var(--text-primary, #111827);
}

.footer-divider {
  margin-top: 3rem;
  margin-bottom: 3rem;
  height: 1px;
  background-color: var(--border-color, #e5e7eb);
}

.footer-copyright {
  text-align: center;
}

.copyright-text {
  color: var(--text-secondary, #6b7280);
  font-size: 0.875rem;
}
</style>
