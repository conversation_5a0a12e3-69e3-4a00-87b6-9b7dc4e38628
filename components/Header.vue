<template>
  <div class="header-container">
    <!-- Logo -->
    <NuxtLink to="/" class="logo-link">
      <div class="logo-icon">
        <span class="logo-text">牛</span>
      </div>
      <span class="logo-name">牛马学城</span>
    </NuxtLink>

    <!-- Right section with navigation and action buttons -->
    <div class="header-right">
      <!-- Desktop Navigation -->
      <nav class="desktop-nav">
        <NuxtLink
          v-for="item in navItems"
          :key="item.path"
          :to="item.path"
          class="nav-link"
          active-class="nav-link-active"
        >
          {{ item.name }}
        </NuxtLink>
      </nav>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <!-- Theme Toggle -->
        <button
          class="theme-toggle"
          @click="toggleTheme"
          :title="isDark ? '切换到明亮模式' : '切换到暗黑模式'"
        >
          <svg
            v-if="isDark"
            class="theme-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <circle cx="12" cy="12" r="5" stroke-width="2" />
            <path
              d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"
              stroke-width="2"
            />
          </svg>
          <svg
            v-else
            class="theme-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path
              d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
              stroke-width="2"
            />
          </svg>
        </button>

        <!-- Login Button -->
        <n-button
          strong
          secondary
          type="info"
          size="small"
          class="login-button"
        >
          登录
        </n-button>

        <!-- Mobile Menu Button -->
        <n-button
          class="mobile-menu-button"
          circle
          quaternary
          @click="isMobileMenuOpen = true"
        >
          <template #icon>
            <svg
              class="mobile-menu-icon"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path
                d="M3 12h18M3 6h18M3 18h18"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </template>
        </n-button>
      </div>
    </div>
  </div>

  <!-- Mobile Navigation -->
  <n-drawer v-model:show="isMobileMenuOpen" :width="280" placement="right">
    <n-drawer-content title="导航菜单">
      <n-menu
        :options="menuOptions"
        :value="activeKey"
        @update:value="handleMobileMenuSelect"
      />
    </n-drawer-content>
  </n-drawer>
</template>

<script setup>
import { h, ref, onMounted, nextTick } from "vue";
import { NIcon } from "naive-ui"; // 只导入需要在JS中使用的组件

// 使用全局的主题管理 composable
const { isDark, toggleTheme } = useTheme();
const route = useRoute();
const router = useRouter();

const isMobileMenuOpen = ref(false);
const hasDarkClass = ref(false);

onMounted(() => {
  nextTick(() => {
    hasDarkClass.value = document.documentElement.classList.contains("dark");
  });
});

// 导航项目
const navItems = [
  { name: "主页", path: "/" },
  { name: "精选", path: "/articles" },
  { name: "课程", path: "/course" },
  { name: "会员", path: "/membership" },
];

// 菜单选项配置
const menuOptions = [
  {
    label: "主页",
    key: "/",
    icon: () =>
      h(NIcon, null, {
        default: () =>
          h(
            "svg",
            {
              class: "menu-icon",
              viewBox: "0 0 24 24",
              fill: "none",
              stroke: "currentColor",
            },
            [
              h("path", {
                d: "M3 12L5 10M5 10L12 3L19 10M5 10V20A1 1 0 006 21H9M19 10L21 12M19 10V20A1 1 0 0018 21H15M9 21V16A1 1 0 0110 15H14A1 1 0 0115 16V21M9 21H15",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
              }),
            ]
          ),
      }),
  },
  {
    label: "文章",
    key: "/articles",
    icon: () =>
      h(NIcon, null, {
        default: () =>
          h(
            "svg",
            {
              class: "menu-icon",
              viewBox: "0 0 24 24",
              fill: "none",
              stroke: "currentColor",
            },
            [
              h("path", {
                d: "M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z",
                "stroke-width": "2",
              }),
              h("path", {
                d: "M9 9H15",
                "stroke-width": "2",
                "stroke-linecap": "round",
              }),
              h("path", {
                d: "M9 13H15",
                "stroke-width": "2",
                "stroke-linecap": "round",
              }),
            ]
          ),
      }),
  },
  {
    label: "会员",
    key: "/membership",
    icon: () =>
      h(NIcon, null, {
        default: () =>
          h(
            "svg",
            {
              class: "menu-icon",
              viewBox: "0 0 24 24",
              fill: "none",
              stroke: "currentColor",
            },
            [
              h("path", {
                d: "M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",
                "stroke-width": "2",
                "stroke-linecap": "round",
                "stroke-linejoin": "round",
              }),
            ]
          ),
      }),
  },
];

// 当前激活的菜单项
const activeKey = computed(() => {
  return route.path;
});

// 处理移动端菜单选择
const handleMobileMenuSelect = (key) => {
  isMobileMenuOpen.value = false;
  router.push(key);
};
</script>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

@media (min-width: 768px) {
  .header-container {
    height: 5rem;
  }
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  gap: 0.5rem;
}

.logo-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  background-color: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  color: #ffffff;
  font-weight: bold;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.logo-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  transition: color 0.3s ease;
}

:global(html.dark) .logo-name {
  color: #ffffff;
}

/* 增加更具体的选择器确保样式应用 */
:global(.dark) .logo-name {
  color: #ffffff;
}

/* 使用CSS变量支持主题切换 */
.logo-name {
  color: var(--text-primary, #111827);
}

/* Debug styles - can be removed in production */
.debug-theme {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  padding: 0.5rem;
  background: #3b82f6;
  color: white;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  z-index: 9999;
}

.desktop-nav {
  display: none;
  gap: 0.25rem;
}

@media (min-width: 768px) {
  .desktop-nav {
    display: flex;
    gap: 0.25rem;
  }
}

.nav-link {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  color: var(--text-color, #374151);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s, color 0.2s;
}

.nav-link:hover {
  background-color: var(--hover-bg-color, #f3f4f6);
  color: var(--text-color, #374151);
}

.nav-link-active {
  background-color: var(--active-bg-color, #f3f4f6);
  color: var(--active-text-color, #111827);
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.theme-toggle {
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: var(--text-color, #374151);
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.theme-toggle:hover {
  background-color: var(--hover-bg-color, #f3f4f6);
}

.theme-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.login-button {
  display: none;
}

@media (min-width: 768px) {
  .login-button {
    display: inline-flex;
  }
}

.mobile-menu-button {
  display: block;
}

@media (min-width: 768px) {
  .mobile-menu-button {
    display: none;
  }
}

.menu-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* 新增样式：导航右对齐 */
.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

@media (min-width: 768px) {
  .header-right {
    gap: 1.5rem;
  }
}

/* 调整导航样式 */
.desktop-nav {
  display: none;
  gap: 0.25rem;
  margin-right: auto;
}

@media (min-width: 768px) {
  .desktop-nav {
    display: flex;
    gap: 0.25rem;
    margin-right: 0;
  }
}

/* 导航右对齐并紧挨登录按钮 */
.header-right {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.desktop-nav {
  margin-right: 0;
  margin-left: auto;
  display: flex;
  gap: 0.25rem;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0;
}
</style>
