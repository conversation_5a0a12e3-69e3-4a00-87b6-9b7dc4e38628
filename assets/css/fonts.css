/* 全局样式定义 */

/* CSS 变量定义 */
:root {
  /* 明亮主题变量 */
  --body-color: #ffffff;
  --text-color: #374151;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --hover-bg-color: #f3f4f6;
  --active-bg-color: #f3f4f6;
  --active-text-color: #111827;
  --border-color: #e5e7eb;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --header-bg: rgba(255, 255, 255, 0.8);
}

/* 添加苹果风格的字体和排版 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  margin: 0;
  padding: 0;
  background-color: var(--body-color);
  color: var(--text-primary);
}

/* 苹果风格的按钮和交互元素 */
.btn-apple {
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  border: none;
  cursor: pointer;
}

.btn-apple::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.btn-apple:hover::after {
  opacity: 1;
}

/* 苹果风格的卡片 */
.card-apple {
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.card-apple:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

/* 苹果风格的输入框 */
.input-apple {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 0.75rem 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: inherit;
  font-size: 1rem;
  width: 100%;
  box-sizing: border-box;
}

.input-apple:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}



/* 苹果风格的渐变背景 */
.bg-gradient-apple {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 苹果风格的圆角 */
.rounded-apple {
  border-radius: 18px;
}

.rounded-apple-sm {
  border-radius: 12px;
}

.rounded-apple-lg {
  border-radius: 24px;
}

/* 苹果风格的阴影 */
.shadow-apple {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.08);
}

.shadow-apple-lg {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
}



/* 平滑过渡动画 */
.transition-apple {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 苹果风格的分隔线 */
.divider-apple {
  height: 1px;
  background-color: #e5e7eb;
}



/* 通用样式 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}