// 开发环境专用配置 - 用于优化热更新和样式加载性能
export default defineNuxtConfig({
  extends: './nuxt.config.ts',
  
  // 开发环境特定优化
  dev: true,
  
  // 关闭生产环境功能以提升开发性能
  experimental: {
    payloadExtraction: false,
    viewTransition: false, // 开发环境关闭视图过渡
    typedPages: false // 关闭类型化页面以提升构建速度
  },
  
  // 开发服务器优化
  devServer: {
    host: 'localhost',
    port: 3000
  },
  
  // Vite 开发优化
  vite: {
    clearScreen: false, // 保持终端输出
    logLevel: 'warn', // 减少日志输出
    
    server: {
      hmr: {
        port: 24678,
        overlay: false,
        clientPort: 24678
      },
      watch: {
        usePolling: false, // 使用原生文件监听
        ignored: ['**/node_modules/**', '**/.git/**']
      }
    },
    
    // 开发环境构建优化
    build: {
      sourcemap: true,
      minify: false, // 开发环境不压缩
      rollupOptions: {
        output: {
          manualChunks: undefined // 开发环境不分包
        }
      }
    },
    
    // 依赖预构建优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router'
      ],
      exclude: ['@nuxt/devtools']
    },
    
    // CSS 开发优化
    css: {
      devSourcemap: true,
      preprocessorOptions: {
        scss: {
          additionalData: `@use "sass:math";`,
          charset: false
        }
      }
    }
  },
  
  // 构建优化
  build: {
    analyze: false // 开发环境关闭分析
  },

  // 模块优化
  modules: [
    '@nuxt/scripts',
    '@nuxt/image',
    '@nuxt/icon',
    '@nuxt/fonts',
    '@nuxtjs/tailwindcss'
    // 开发环境移除 eslint 模块以提升性能
  ]
})