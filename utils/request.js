import { API_BASE_URL, REQUEST_TIMEOUT, DEFAULT_HEADERS } from '@/config/api'

// 基于 Nuxt 内置的 $fetch 创建请求实例
const request = $fetch.create({
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT,
  headers: DEFAULT_HEADERS,
  
  // 请求拦截器
  onRequest({ request, options }) {
    // 从 localStorage 获取 token
    if (import.meta.client) {
      const token = localStorage.getItem('token')
      if (token) {
        options.headers = {
          ...options.headers,
          Authorization: `Bearer ${token}`
        }
      }
    }

    // 添加时间戳防止缓存（仅对 GET 请求）
    if (options.method === 'GET' || !options.method) {
      // 处理相对路径的baseURL
      const baseUrl = API_BASE_URL.startsWith('http') ? API_BASE_URL : `${window.location.origin}${API_BASE_URL}`
      const url = new URL(request, baseUrl)
      url.searchParams.set('_t', Date.now().toString())
      return url.toString()
    }
  },
  
  // 响应拦截器 - 成功响应
  onResponse({ response }) {
    // 根据更新后的API文档，直接返回响应数据
    return response._data
  },
  
  // 响应拦截器 - 错误响应
  onResponseError({ response, error }) {
    console.error('请求失败:', error)
    
    // 增强错误对象，添加用户友好的错误信息
    if (response) {
      const { status, _data } = response
      
      switch (status) {
        case 401:
          // 401错误需要特殊处理：清除认证信息并跳转登录页
          if (import.meta.client) {
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
            localStorage.removeItem('refreshToken')
            localStorage.removeItem('permissions')
            localStorage.removeItem('roles')
            window.location.href = '/login'
          }
          error.userMessage = '登录已过期，请重新登录'
          break
        case 403:
          error.userMessage = '没有权限访问该资源'
          break
        case 404:
          error.userMessage = '请求的资源不存在'
          break
        case 500:
          error.userMessage = '服务器内部错误'
          break
        default: {
          // 安全地访问错误信息
          const errorMessage = 
            _data?.message || _data?.error?.message || _data?.errors?.message || `请求失败 (${status})`
          error.userMessage = errorMessage
          break
        }
      }
    } else {
      // 网络错误或其他错误
      if (error.name === 'TimeoutError') {
        error.userMessage = '请求超时，请稍后重试'
      } else if (error.name === 'FetchError') {
        error.userMessage = '网络连接失败，请检查网络'
      } else {
        error.userMessage = '请求配置错误'
      }
    }
    
    // 抛出增强后的错误
    throw error
  }
})

export default request