import { ref, watch, readonly, computed } from "vue";
import { darkTheme } from "naive-ui";

// 主题状态管理
const isDark = ref(false);

export const useTheme = () => {
  // 计算当前主题对象
  const theme = computed(() => {
    return isDark.value ? darkTheme : null;
  });

  // 应用 CSS 变量
  const applyCSSVariables = () => {
    if (import.meta.client) {
      const root = document.documentElement;

      if (isDark.value) {
        // 暗黑主题变量
        root.style.setProperty("--body-color", "#111827");
        root.style.setProperty("--text-color", "#d1d5db");
        root.style.setProperty("--text-primary", "#f9fafb");
        root.style.setProperty("--text-secondary", "#d1d5db");
        root.style.setProperty("--hover-bg-color", "#1f2937");
        root.style.setProperty("--active-bg-color", "#1f2937");
        root.style.setProperty("--active-text-color", "#ffffff");
        root.style.setProperty("--border-color", "#374151");
        root.style.setProperty("--bg-primary", "#1f2937");
        root.style.setProperty("--bg-secondary", "rgba(31, 41, 55, 0.5)");
        root.style.setProperty("--bg-tertiary", "#374151");
        root.style.setProperty("--header-bg", "rgba(17, 24, 39, 0.8)");
      } else {
        // 明亮主题变量
        root.style.setProperty("--body-color", "#ffffff");
        root.style.setProperty("--text-color", "#374151");
        root.style.setProperty("--text-primary", "#111827");
        root.style.setProperty("--text-secondary", "#6b7280");
        root.style.setProperty("--hover-bg-color", "#f3f4f6");
        root.style.setProperty("--active-bg-color", "#f3f4f6");
        root.style.setProperty("--active-text-color", "#111827");
        root.style.setProperty("--border-color", "#e5e7eb");
        root.style.setProperty("--bg-primary", "#ffffff");
        root.style.setProperty("--bg-secondary", "#f9fafb");
        root.style.setProperty("--bg-tertiary", "#f3f4f6");
        root.style.setProperty("--header-bg", "rgba(255, 255, 255, 0.8)");
      }
    }
  };

  // 初始化主题
  const initTheme = () => {
    if (import.meta.client) {
      // 从localStorage获取保存的主题设置
      const savedTheme = localStorage.getItem("theme");

      if (savedTheme) {
        isDark.value = savedTheme === "dark";
      } else {
        // 如果没有保存的设置，使用系统偏好
        isDark.value = window.matchMedia(
          "(prefers-color-scheme: dark)"
        ).matches;
        localStorage.setItem("theme", isDark.value ? "dark" : "light");
      }

      // 应用 CSS 变量
      applyCSSVariables();
    }
  };

  // 切换主题
  const toggleTheme = () => {
    isDark.value = !isDark.value;
    if (import.meta.client) {
      localStorage.setItem("theme", isDark.value ? "dark" : "light");
      applyCSSVariables();
    }
  };

  // 设置特定主题
  const setTheme = (themeType) => {
    isDark.value = themeType === "dark";
    if (import.meta.client) {
      localStorage.setItem("theme", themeType);
      applyCSSVariables();
    }
  };

  // 监听主题变化
  watch(isDark, () => {
    applyCSSVariables();
  });

  // 监听系统主题变化
  if (import.meta.client) {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    mediaQuery.addEventListener("change", (e) => {
      // 只有在没有用户手动设置时才跟随系统
      if (!localStorage.getItem("theme")) {
        isDark.value = e.matches;
      }
    });
  }

  // 调试函数
  const getThemeDebugInfo = () => {
    if (import.meta.client) {
      return {
        isDarkValue: isDark.value,
        localStorage: localStorage.getItem("theme"),
        themeObject: theme.value,
      };
    }
    return {};
  };

  return {
    isDark: readonly(isDark),
    theme: readonly(theme),
    initTheme,
    toggleTheme,
    setTheme,
    getThemeDebugInfo,
  };
};

// 注意：主题初始化现在在 app.vue 中进行，避免重复初始化
// 使用 Naive UI 的主题系统，通过 n-config-provider 的 theme 属性控制
